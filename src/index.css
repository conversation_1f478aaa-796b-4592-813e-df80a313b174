
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 173 98% 47%;
    --primary-foreground: 224 71.4% 4.1%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 173 98% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 中文标题渐变样式 - 完整显示不截断 */
.gradient-title-chinese {
  background: linear-gradient(
    90deg,
    #00FFFF,  /* cyan-400 - 生命游戏 */
    #F59E0B,  /* amber-500 - 谢林隔离模型 */
    #8B5CF6,  /* violet-500 - 二维Ising模型 */
    #10B981,  /* green-500 - 蚁群觅食 */
    #0EA5E9,  /* sky-500 - Boids算法 */
    #00FFFF   /* 回到起始色，形成无缝循环 */
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientFlow 15s linear infinite;
  width: 100%; /* 完整显示，不截断 */
  margin: 0 auto;
}

/* 英文标题渐变样式 */
.gradient-title-english {
  background: linear-gradient(
    90deg,
    #00FFFF,  /* cyan-400 - 生命游戏 */
    #F59E0B,  /* amber-500 - 谢林隔离模型 */
    #8B5CF6,  /* violet-500 - 二维Ising模型 */
    #10B981,  /* green-500 - 蚁群觅食 */
    #0EA5E9,  /* sky-500 - Boids算法 */
    #00FFFF   /* 回到起始色，形成无缝循环 */
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientFlow 18s linear infinite;
  width: 100%;
  margin: 0 auto;
}

/* 混合标题渐变样式 - 用于Index页面 */
.gradient-title {
  background: linear-gradient(
    90deg,
    #00FFFF,  /* cyan-400 - 生命游戏 */
    #F59E0B,  /* amber-500 - 谢林隔离模型 */
    #8B5CF6,  /* violet-500 - 二维Ising模型 */
    #10B981,  /* green-500 - 蚁群觅食 */
    #0EA5E9,  /* sky-500 - Boids算法 */
    #00FFFF   /* 回到起始色，形成无缝循环 */
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientFlow 16s linear infinite;
  width: 100%;
  margin: 0 auto;
}

/* 呼吸灯按钮样式 - 单色循环，5秒周期，二次曲线缓动 */
.breathing-button {
  animation: breathingColors 25s infinite;
  border: none;
}

.breathing-button:hover {
  animation: breathingColors 25s infinite;
}

@keyframes gradientFlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

/* 呼吸灯颜色切换动画 - 每5秒一个颜色周期，使用二次曲线 */
@keyframes breathingColors {
  /* 青色周期 (0-20%) */
  0% {
    background-color: #00FFFF;
    opacity: 0.4;
  }
  4% {
    background-color: #00FFFF;
    opacity: 1;
  }
  16% {
    background-color: #00FFFF;
    opacity: 0.4;
  }
  20% {
    background-color: #00FFFF;
    opacity: 0.4;
  }
  
  /* 琥珀色周期 (20-40%) */
  20% {
    background-color: #F59E0B;
    opacity: 0.4;
  }
  24% {
    background-color: #F59E0B;
    opacity: 1;
  }
  36% {
    background-color: #F59E0B;
    opacity: 0.4;
  }
  40% {
    background-color: #F59E0B;
    opacity: 0.4;
  }
  
  /* 紫色周期 (40-60%) */
  40% {
    background-color: #8B5CF6;
    opacity: 0.4;
  }
  44% {
    background-color: #8B5CF6;
    opacity: 1;
  }
  56% {
    background-color: #8B5CF6;
    opacity: 0.4;
  }
  60% {
    background-color: #8B5CF6;
    opacity: 0.4;
  }
  
  /* 绿色周期 (60-80%) */
  60% {
    background-color: #10B981;
    opacity: 0.4;
  }
  64% {
    background-color: #10B981;
    opacity: 1;
  }
  76% {
    background-color: #10B981;
    opacity: 0.4;
  }
  80% {
    background-color: #10B981;
    opacity: 0.4;
  }
  
  /* 天蓝色周期 (80-100%) */
  80% {
    background-color: #0EA5E9;
    opacity: 0.4;
  }
  84% {
    background-color: #0EA5E9;
    opacity: 1;
  }
  96% {
    background-color: #0EA5E9;
    opacity: 0.4;
  }
  100% {
    background-color: #0EA5E9;
    opacity: 0.4;
  }
}
