
import { useState, useEffect, useRef, useCallback } from 'react';
import {
  Grid,
  AgentType,
  SchellingParams,
  SchellingStats,
  shuffle,
} from '@/lib/schelling-segregation/core';

const useSchellingModel = (params: SchellingParams, isActive: boolean) => {
  const [grid, setGrid] = useState<Grid>([]);
  const [stats, setStats] = useState<SchellingStats>({
    round: 0,
    happyAgentPct: 0,
    segregationIndex: 0,
    isConverged: false,
  });
  const [isRunning, setIsRunning] = useState(false);
  const animationFrameId = useRef<number>();

  const setup = useCallback(() => {
    const newGrid: Grid = Array(params.gridSize)
      .fill(0)
      .map(() => Array(params.gridSize).fill(0));
    
    const totalCells = params.gridSize * params.gridSize;
    const emptyCount = Math.floor(totalCells * params.emptyCellsPct);
    const agentCount = totalCells - emptyCount;
    const typeACount = Math.floor(agentCount * params.ratioTypeA);
    const typeBCount = agentCount - typeACount;

    let agents: AgentType[] = [
      ...Array(typeACount).fill(1),
      ...Array(typeBCount).fill(2),
      ...Array(emptyCount).fill(0),
    ];
    agents = shuffle(agents);

    for (let i = 0; i < params.gridSize; i++) {
      for (let j = 0; j < params.gridSize; j++) {
        newGrid[i][j] = agents[i * params.gridSize + j];
      }
    }
    setGrid(newGrid);
    setStats({ round: 0, happyAgentPct: 0, segregationIndex: 0, isConverged: false });
    setIsRunning(false);
  }, [params]);

  useEffect(() => {
    setup();
  }, [setup]);

  // Stop animation when not active
  useEffect(() => {
    if (!isActive && animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
      setIsRunning(false);
    }
  }, [isActive]);

  const step = useCallback(() => {
    if (!isActive) return;
    
    setGrid(currentGrid => {
      const size = params.gridSize;
      const unhappyAgents: { x: number; y: number }[] = [];
      const emptyCells: { x: number; y: number }[] = [];
      let totalAgents = 0;
      let happyAgents = 0;
      let totalSameNeighborRatio = 0;

      const getNeighbors = (x: number, y: number) => {
        const neighbors: { x: number; y: number }[] = [];
        const deltas = params.neighborhoodType === 'Moore' 
          ? [[-1,-1],[-1,0],[-1,1],[0,-1],[0,1],[1,-1],[1,0],[1,1]] 
          : [[-1,0],[1,0],[0,-1],[0,1]];

        for (const [dx, dy] of deltas) {
          let nx = x + dx;
          let ny = y + dy;
          if (params.boundaryCondition === 'Periodic') {
            nx = (nx + size) % size;
            ny = (ny + size) % size;
            neighbors.push({ x: nx, y: ny });
          } else { // Cut-off
            if (nx >= 0 && nx < size && ny >= 0 && ny < size) {
              neighbors.push({ x: nx, y: ny });
            }
          }
        }
        return neighbors;
      };

      for (let y = 0; y < size; y++) {
        for (let x = 0; x < size; x++) {
          const agentType = currentGrid[y][x];
          if (agentType === 0) {
            emptyCells.push({ x, y });
            continue;
          }

          totalAgents++;
          const neighbors = getNeighbors(x, y);
          const neighborAgents = neighbors.map(n => currentGrid[n.y][n.x]).filter(n => n !== 0);

          if (neighborAgents.length === 0) {
            happyAgents++;
            continue;
          }

          const sameTypeNeighbors = neighborAgents.filter(n => n === agentType).length;
          const similarityRatio = sameTypeNeighbors / neighborAgents.length;
          
          totalSameNeighborRatio += similarityRatio;

          if (similarityRatio < params.similarityThreshold) {
            unhappyAgents.push({ x, y });
          } else {
            happyAgents++;
          }
        }
      }

      if (unhappyAgents.length > 0) {
        shuffle(unhappyAgents);
        shuffle(emptyCells);
        const newGrid = currentGrid.map(row => [...row]);
        for (let i = 0; i < Math.min(unhappyAgents.length, emptyCells.length); i++) {
          const agentPos = unhappyAgents[i];
          const emptyPos = emptyCells[i];
          const agentType = newGrid[agentPos.y][agentPos.x];

          newGrid[emptyPos.y][emptyPos.x] = agentType;
          newGrid[agentPos.y][agentPos.x] = 0;
        }
        setStats(s => ({
            ...s,
            round: s.round + 1,
            happyAgentPct: totalAgents > 0 ? (happyAgents / totalAgents) : 1,
            segregationIndex: totalAgents > 0 ? (totalSameNeighborRatio / totalAgents) : 0,
            isConverged: false,
        }));
        return newGrid;
      } else {
         setStats(s => ({ ...s, isConverged: true, happyAgentPct: 1 }));
         setIsRunning(false);
         return currentGrid;
      }
    });
  }, [params, isActive]);

  const runSimulation = useCallback(() => {
    if (isRunning && isActive) {
      step();
      animationFrameId.current = requestAnimationFrame(runSimulation);
    }
  }, [isRunning, step, isActive]);

  useEffect(() => {
    if (isRunning && isActive) {
      animationFrameId.current = requestAnimationFrame(runSimulation);
    } else {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    }
    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [isRunning, runSimulation, isActive]);

  const togglePlay = () => {
    if (!isActive) return;
    setIsRunning(prev => !prev);
  };
  
  const reset = () => setup();

  return { grid, stats, isRunning, togglePlay, step, reset };
};

export default useSchellingModel;
