
import { useState, useCallback, useRef, useEffect } from 'react';
import { createGrid, computeNextGeneration, GameRules } from '@/lib/game-of-life/core';
import { Pattern } from '@/lib/game-of-life/patterns';

interface GameOfLifeConfig {
  width: number;
  height: number;
  speed: number;
  wrapEdges: boolean;
  rules: GameRules;
  isActive: boolean;
}

export function useGameOfLife(config: GameOfLifeConfig) {
  const [grid, setGrid] = useState(() => createGrid(config.width, config.height));
  const [isRunning, setIsRunning] = useState(false);
  const [generation, setGeneration] = useState(0);
  const [liveCellCount, setLiveCellCount] = useState(0);
  
  const runningRef = useRef(isRunning);
  runningRef.current = isRunning;

  const speedRef = useRef(config.speed);
  speedRef.current = config.speed;

  const animationFrameId = useRef<number>();
  const lastUpdateTime = useRef(0);

  const runSimulation = useCallback((timestamp: number) => {
    if (!runningRef.current || !config.isActive) return;

    if (timestamp - lastUpdateTime.current >= speedRef.current) {
      lastUpdateTime.current = timestamp;
      setGrid((g) => {
        const { nextGrid, liveCells } = computeNextGeneration(g, config.width, config.height, config.wrapEdges, config.rules);
        setLiveCellCount(liveCells);
        return nextGrid;
      });
      setGeneration((g) => g + 1);
    }
    
    if (config.isActive) {
      animationFrameId.current = requestAnimationFrame(runSimulation);
    }
  }, [config.width, config.height, config.wrapEdges, config.rules, config.isActive]);

  // Stop animation when not active
  useEffect(() => {
    if (!config.isActive && animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
      setIsRunning(false);
    }
  }, [config.isActive]);

  const togglePlay = useCallback(() => {
    if (!config.isActive) return;
    
    setIsRunning(!isRunning);
    if (!isRunning) {
      runningRef.current = true;
      lastUpdateTime.current = performance.now();
      animationFrameId.current = requestAnimationFrame(runSimulation);
    } else {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    }
  }, [isRunning, runSimulation, config.isActive]);

  const step = useCallback(() => {
    if (isRunning || !config.isActive) return;
    setGrid((g) => {
      const { nextGrid, liveCells } = computeNextGeneration(g, config.width, config.height, config.wrapEdges, config.rules);
      setLiveCellCount(liveCells);
      return nextGrid;
    });
    setGeneration((g) => g + 1);
  }, [isRunning, config.width, config.height, config.wrapEdges, config.rules, config.isActive]);

  const reset = useCallback(() => {
    setIsRunning(false);
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
    }
    setGrid(createGrid(config.width, config.height));
    setGeneration(0);
    setLiveCellCount(0);
  }, [config.width, config.height]);
  
  const setCellState = useCallback((row: number, col: number, state: number) => {
      if (!config.isActive) return;
      const newGrid = grid.map(r => [...r]);
      if (row >= 0 && row < config.height && col >= 0 && col < config.width) {
        newGrid[row][col] = state;
        setGrid(newGrid);
        setLiveCellCount(c => c + (state === 1 ? 1 : (grid[row][col] === 1 ? -1 : 0)));
      }
  }, [grid, config.width, config.height, config.isActive]);

  const loadPattern = useCallback((pattern: Pattern) => {
      if (!config.isActive) return;
      reset();
      const newGrid = createGrid(config.width, config.height);
      const offsetX = Math.floor(config.width / 2 - Math.max(...pattern.map(p => p[0])) / 2);
      const offsetY = Math.floor(config.height / 2 - Math.max(...pattern.map(p => p[1])) / 2);
      let liveCells = 0;
      pattern.forEach(([x, y]) => {
          const gridX = x + offsetX;
          const gridY = y + offsetY;
          if (gridX >= 0 && gridX < config.width && gridY >= 0 && gridY < config.height) {
              newGrid[gridY][gridX] = 1;
              liveCells++;
          }
      });
      setGrid(newGrid);
      setLiveCellCount(liveCells);
      setGeneration(0);
  }, [config.width, config.height, config.isActive, reset]);

  return { grid, isRunning, generation, liveCellCount, togglePlay, step, reset, setCellState, loadPattern };
}
