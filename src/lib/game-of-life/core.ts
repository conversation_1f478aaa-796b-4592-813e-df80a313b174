export function createGrid(width: number, height: number): number[][] {
  return Array.from({ length: height }, () => Array(width).fill(0));
}

export interface GameRules {
  survivalMin: number;
  survivalMax: number;
  birthMin: number;
  birthMax: number;
}

export function computeNextGeneration(grid: number[][], width: number, height: number, wrapEdges: boolean, rules: GameRules): { nextGrid: number[][], liveCells: number } {
  const nextGrid = createGrid(width, height);
  let liveCells = 0;

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      let liveNeighbors = 0;
      for (let i = -1; i <= 1; i++) {
        for (let j = -1; j <= 1; j++) {
          if (i === 0 && j === 0) continue;

          let nx = x + j;
          let ny = y + i;

          if (wrapEdges) {
            nx = (nx + width) % width;
            ny = (ny + height) % height;
          }

          if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
            liveNeighbors += grid[ny][nx];
          }
        }
      }

      const cell = grid[y][x];
      if (cell === 1) {
        if (liveNeighbors >= rules.survivalMin && liveNeighbors <= rules.survivalMax) {
          nextGrid[y][x] = 1;
        }
      } else {
        if (liveNeighbors >= rules.birthMin && liveNeighbors <= rules.birthMax) {
          nextGrid[y][x] = 1;
        }
      }
      
      if (nextGrid[y][x] === 1) {
        liveCells++;
      }
    }
  }

  return { nextGrid, liveCells };
}
