
// GPU-accelerated Ant Colony Optimization Simulation
export const ANT_STATE_SEARCHING = 'searching';
export const ANT_STATE_RETURNING = 'returning';

export type AntState = typeof ANT_STATE_SEARCHING | typeof ANT_STATE_RETURNING;

export interface Ant {
  id: number;
  x: number;
  y: number;
  angle: number;
  state: AntState;
}

export interface Point {
  x: number;
  y: number;
}

export interface SimulationParams {
  antCount: number;
  evaporationRate: number;
  pheromoneStrength: number;
  trailStrength: number;
  antSpeed: number;
  sensorDistance: number;
  sensorAngle: number;
  turnSpeed: number;
}

export class GPUAntColonySimulation {
  public ants: Ant[] = [];
  public width: number;
  public height: number;
  public nestPosition: Point | null = null;
  public foodSources: Point[] = [];
  
  private params: SimulationParams;
  private gl: WebGL2RenderingContext;
  private canvas: HTMLCanvasElement;
  
  // WebGL resources
  private antTexture: WebGLTexture | null = null;
  private pheromoneTexture: WebGLTexture | null = null;
  private obstacleTexture: WebGLTexture | null = null;
  private framebuffer: WebGLFramebuffer | null = null;
  private antProgram: WebGLProgram | null = null;
  private pheromoneProgram: WebGLProgram | null = null;
  private renderProgram: WebGLProgram | null = null;
  
  // Buffers
  private quadBuffer: WebGLBuffer | null = null;
  private antData: Float32Array;
  private pheromoneData: Float32Array;
  private obstacleData: Uint8Array;

  constructor(width: number, height: number, params: SimulationParams, canvas: HTMLCanvasElement) {
    this.width = Math.floor(width);
    this.height = Math.floor(height);
    this.params = params;
    this.canvas = canvas;
    
    const gl = canvas.getContext('webgl2', { 
      antialias: false,
      powerPreference: 'high-performance'
    });
    if (!gl) {
      throw new Error('WebGL2 not supported');
    }
    this.gl = gl;
    
    this.antData = new Float32Array(params.antCount * 4); // x, y, angle, state
    this.pheromoneData = new Float32Array(width * height * 2); // food, home pheromones
    this.obstacleData = new Uint8Array(width * height);
    
    this.initializeWebGL();
  }

  private initializeWebGL() {
    const gl = this.gl;
    
    // Create quad buffer for full-screen rendering
    const quadVertices = new Float32Array([
      -1, -1, 0, 0,
       1, -1, 1, 0,
      -1,  1, 0, 1,
       1,  1, 1, 1
    ]);
    
    this.quadBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, quadVertices, gl.STATIC_DRAW);
    
    // Create textures
    this.antTexture = this.createTexture(this.params.antCount, 1, gl.RGBA32F);
    this.pheromoneTexture = this.createTexture(this.width, this.height, gl.RG32F);
    this.obstacleTexture = this.createTexture(this.width, this.height, gl.R8);
    
    // Create framebuffer
    this.framebuffer = gl.createFramebuffer();
    
    // Create shader programs
    this.antProgram = this.createProgram(this.vertexShader, this.antUpdateShader);
    this.pheromoneProgram = this.createProgram(this.vertexShader, this.pheromoneUpdateShader);
    this.renderProgram = this.createProgram(this.vertexShader, this.renderShader);
  }

  private createTexture(width: number, height: number, format: number): WebGLTexture {
    const gl = this.gl;
    const texture = gl.createTexture();
    if (!texture) throw new Error('Failed to create texture');
    
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    
    if (format === gl.RGBA32F) {
      gl.texImage2D(gl.TEXTURE_2D, 0, format, width, height, 0, gl.RGBA, gl.FLOAT, null);
    } else if (format === gl.RG32F) {
      gl.texImage2D(gl.TEXTURE_2D, 0, format, width, height, 0, gl.RG, gl.FLOAT, null);
    } else {
      gl.texImage2D(gl.TEXTURE_2D, 0, format, width, height, 0, gl.RED, gl.UNSIGNED_BYTE, null);
    }
    
    return texture;
  }

  private createProgram(vertexSource: string, fragmentSource: string): WebGLProgram {
    const gl = this.gl;
    const program = gl.createProgram();
    if (!program) throw new Error('Failed to create program');
    
    const vertexShader = this.compileShader(vertexSource, gl.VERTEX_SHADER);
    const fragmentShader = this.compileShader(fragmentSource, gl.FRAGMENT_SHADER);
    
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      throw new Error('Shader program linking failed: ' + gl.getProgramInfoLog(program));
    }
    
    return program;
  }

  private compileShader(source: string, type: number): WebGLShader {
    const gl = this.gl;
    const shader = gl.createShader(type);
    if (!shader) throw new Error('Failed to create shader');
    
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      throw new Error('Shader compilation failed: ' + gl.getShaderInfoLog(shader));
    }
    
    return shader;
  }

  private get vertexShader(): string {
    return `#version 300 es
    in vec2 a_position;
    in vec2 a_texCoord;
    out vec2 v_texCoord;
    
    void main() {
      gl_Position = vec4(a_position, 0.0, 1.0);
      v_texCoord = a_texCoord;
    }`;
  }

  private get antUpdateShader(): string {
    return `#version 300 es
    precision highp float;
    
    uniform sampler2D u_ants;
    uniform sampler2D u_pheromones;
    uniform sampler2D u_obstacles;
    uniform vec2 u_resolution;
    uniform vec2 u_nestPos;
    uniform vec2 u_foodPos[4];
    uniform int u_foodCount;
    uniform float u_sensorDistance;
    uniform float u_sensorAngle;
    uniform float u_turnSpeed;
    uniform float u_antSpeed;
    uniform float u_time;
    
    in vec2 v_texCoord;
    out vec4 fragColor;
    
    float random(vec2 st) {
      return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123 + u_time);
    }
    
    void main() {
      int antIndex = int(v_texCoord.x * float(textureSize(u_ants, 0).x));
      vec4 antData = texelFetch(u_ants, ivec2(antIndex, 0), 0);
      
      vec2 pos = antData.xy;
      float angle = antData.z;
      float state = antData.w; // 0 = searching, 1 = returning
      
      // Sensor positions
      vec2 sensorOffset = u_sensorDistance / u_resolution;
      vec2 frontSensor = pos + vec2(cos(angle), sin(angle)) * sensorOffset;
      vec2 leftSensor = pos + vec2(cos(angle - u_sensorAngle), sin(angle - u_sensorAngle)) * sensorOffset;
      vec2 rightSensor = pos + vec2(cos(angle + u_sensorAngle), sin(angle + u_sensorAngle)) * sensorOffset;
      
      // Clamp sensors to texture bounds
      frontSensor = clamp(frontSensor, vec2(0.0), vec2(1.0));
      leftSensor = clamp(leftSensor, vec2(0.0), vec2(1.0));
      rightSensor = clamp(rightSensor, vec2(0.0), vec2(1.0));
      
      // Sample pheromones
      float frontPheromone, leftPheromone, rightPheromone;
      if (state < 0.5) { // searching for food
        frontPheromone = texture(u_pheromones, frontSensor).r; // food pheromone
        leftPheromone = texture(u_pheromones, leftSensor).r;
        rightPheromone = texture(u_pheromones, rightSensor).r;
      } else { // returning home
        frontPheromone = texture(u_pheromones, frontSensor).g; // home pheromone
        leftPheromone = texture(u_pheromones, leftSensor).g;
        rightPheromone = texture(u_pheromones, rightSensor).g;
      }
      
      // Steering logic
      float newAngle = angle;
      float maxPheromone = max(max(frontPheromone, leftPheromone), rightPheromone);
      
      if (maxPheromone > 0.01) {
        if (leftPheromone > frontPheromone && leftPheromone > rightPheromone) {
          newAngle -= u_turnSpeed;
        } else if (rightPheromone > frontPheromone && rightPheromone > leftPheromone) {
          newAngle += u_turnSpeed;
        }
      } else {
        // Random exploration when no pheromone detected
        newAngle += (random(pos + u_time) - 0.5) * u_turnSpeed * 2.0;
      }
      
      // Move ant
      vec2 direction = vec2(cos(newAngle), sin(newAngle));
      vec2 newPos = pos + direction * u_antSpeed / u_resolution;
      
      // Boundary checking with bounce
      if (newPos.x <= 0.01 || newPos.x >= 0.99) {
        newAngle = 3.14159 - newAngle;
        direction = vec2(cos(newAngle), sin(newAngle));
      }
      if (newPos.y <= 0.01 || newPos.y >= 0.99) {
        newAngle = -newAngle;
        direction = vec2(cos(newAngle), sin(newAngle));
      }
      
      newPos = clamp(pos + direction * u_antSpeed / u_resolution, vec2(0.01), vec2(0.99));
      
      // Check for obstacles
      if (texture(u_obstacles, newPos).r > 0.5) {
        newAngle += 3.14159; // turn around
        newPos = pos; // don't move
      }
      
      // State transitions
      float newState = state;
      if (state < 0.5) { // searching for food
        for (int i = 0; i < u_foodCount && i < 4; i++) {
          vec2 foodPos = u_foodPos[i] / u_resolution;
          if (distance(newPos, foodPos) < 20.0 / u_resolution.x) {
            newState = 1.0; // switch to returning
            newAngle += 3.14159; // turn around
            break;
          }
        }
      } else { // returning home
        vec2 nestPos = u_nestPos / u_resolution;
        if (distance(newPos, nestPos) < 20.0 / u_resolution.x) {
          newState = 0.0; // switch to searching
          newAngle += 3.14159 + (random(pos) - 0.5) * 1.0; // turn around with some randomness
        }
      }
      
      fragColor = vec4(newPos, newAngle, newState);
    }`;
  }

  private get pheromoneUpdateShader(): string {
    return `#version 300 es
    precision highp float;
    
    uniform sampler2D u_pheromones;
    uniform sampler2D u_ants;
    uniform vec2 u_resolution;
    uniform float u_evaporationRate;
    uniform float u_pheromoneStrength;
    uniform int u_antCount;
    
    in vec2 v_texCoord;
    out vec4 fragColor;
    
    void main() {
      vec2 currentPheromones = texture(u_pheromones, v_texCoord).rg;
      
      // Evaporation
      currentPheromones *= u_evaporationRate;
      
      // Simple diffusion
      vec2 texelSize = 1.0 / u_resolution;
      vec2 blurred = vec2(0.0);
      float totalWeight = 0.0;
      
      for (int i = -1; i <= 1; i++) {
        for (int j = -1; j <= 1; j++) {
          vec2 offset = vec2(float(i), float(j)) * texelSize;
          vec2 samplePos = v_texCoord + offset;
          if (samplePos.x >= 0.0 && samplePos.x <= 1.0 && samplePos.y >= 0.0 && samplePos.y <= 1.0) {
            float weight = 1.0 / (1.0 + length(offset) * u_resolution.x);
            blurred += texture(u_pheromones, samplePos).rg * weight;
            totalWeight += weight;
          }
        }
      }
      if (totalWeight > 0.0) {
        blurred /= totalWeight;
        currentPheromones = mix(currentPheromones, blurred, 0.1);
      }
      
      // Add pheromones from ants
      vec2 worldPos = v_texCoord * u_resolution;
      for (int i = 0; i < u_antCount && i < 512; i++) {
        vec4 antData = texelFetch(u_ants, ivec2(i, 0), 0);
        vec2 antPos = antData.xy * u_resolution;
        float antState = antData.w;
        
        float dist = distance(worldPos, antPos);
        if (dist < 3.0) {
          float strength = u_pheromoneStrength * (1.0 - dist / 3.0);
          if (antState < 0.5) { // searching - leave home pheromone
            currentPheromones.g += strength;
          } else { // returning - leave food pheromone
            currentPheromones.r += strength;
          }
        }
      }
      
      fragColor = vec4(clamp(currentPheromones, 0.0, 100.0), 0.0, 1.0);
    }`;
  }

  private get renderShader(): string {
    return `#version 300 es
    precision highp float;
    
    uniform sampler2D u_pheromones;
    uniform sampler2D u_ants;
    uniform sampler2D u_obstacles;
    uniform vec2 u_resolution;
    uniform vec2 u_nestPos;
    uniform vec2 u_foodPos[4];
    uniform int u_foodCount;
    uniform int u_antCount;
    
    in vec2 v_texCoord;
    out vec4 fragColor;
    
    void main() {
      vec2 worldPos = v_texCoord * u_resolution;
      vec4 color = vec4(0.05, 0.05, 0.05, 1.0); // dark background
      
      // Render pheromones with higher contrast
      vec2 pheromones = texture(u_pheromones, v_texCoord).rg;
      color.rgb += vec3(pheromones.r * 0.6, pheromones.r * 0.2, 0.0); // food pheromone (red-orange)
      color.rgb += vec3(0.0, pheromones.g * 0.2, pheromones.g * 0.6); // home pheromone (blue)
      
      // Render obstacles
      float obstacle = texture(u_obstacles, v_texCoord).r;
      if (obstacle > 0.5) {
        color.rgb = vec3(0.7, 0.7, 0.7);
      }
      
      // Render nest (larger and more visible)
      if (distance(worldPos, u_nestPos) < 15.0) {
        float dist = distance(worldPos, u_nestPos);
        float intensity = 1.0 - (dist / 15.0);
        color.rgb = mix(color.rgb, vec3(1.0, 0.6, 0.2), intensity * 0.8); // orange nest
      }
      
      // Render food sources (larger and more visible)
      for (int i = 0; i < u_foodCount && i < 4; i++) {
        float dist = distance(worldPos, u_foodPos[i]);
        if (dist < 12.0) {
          float intensity = 1.0 - (dist / 12.0);
          color.rgb = mix(color.rgb, vec3(0.2, 1.0, 0.2), intensity * 0.8); // bright green food
        }
      }
      
      // Render ants with high contrast
      for (int i = 0; i < u_antCount && i < 512; i++) {
        vec4 antData = texelFetch(u_ants, ivec2(i, 0), 0);
        vec2 antPos = antData.xy * u_resolution;
        float antState = antData.w;
        
        float dist = distance(worldPos, antPos);
        if (dist < 2.5) {
          if (antState < 0.5) {
            color.rgb = vec3(1.0, 1.0, 1.0); // white for searching
          } else {
            color.rgb = vec3(1.0, 1.0, 0.0); // yellow for returning with food
          }
        }
      }
      
      fragColor = color;
    }`;
  }

  private initializeAnts() {
    if (!this.nestPosition) {
      console.warn('No nest position set, cannot initialize ants');
      return;
    }
    
    console.log('Initializing ants at nest position:', this.nestPosition);
    
    for (let i = 0; i < this.params.antCount; i++) {
      const angle = Math.random() * 2 * Math.PI;
      const distance = Math.random() * 10 + 5; // Random distance from nest
      
      const x = Math.max(20, Math.min(this.width - 20, this.nestPosition.x + distance * Math.cos(angle)));
      const y = Math.max(20, Math.min(this.height - 20, this.nestPosition.y + distance * Math.sin(angle)));
      
      this.antData[i * 4] = x / this.width;
      this.antData[i * 4 + 1] = y / this.height;
      this.antData[i * 4 + 2] = Math.random() * 2 * Math.PI;
      this.antData[i * 4 + 3] = 0; // searching state
    }
    
    // Upload to GPU
    this.uploadAntData();
    console.log('Ants initialized:', this.params.antCount);
  }

  private uploadAntData() {
    const gl = this.gl;
    gl.bindTexture(gl.TEXTURE_2D, this.antTexture);
    gl.texSubImage2D(gl.TEXTURE_2D, 0, 0, 0, this.params.antCount, 1, gl.RGBA, gl.FLOAT, this.antData);
  }

  public update() {
    const gl = this.gl;
    
    // Update ants
    this.runComputeShader(this.antProgram!, this.antTexture!);
    
    // Update pheromones
    this.runComputeShader(this.pheromoneProgram!, this.pheromoneTexture!);
    
    // Render to canvas
    this.render();
  }

  private runComputeShader(program: WebGLProgram, outputTexture: WebGLTexture) {
    const gl = this.gl;
    
    gl.bindFramebuffer(gl.FRAMEBUFFER, this.framebuffer);
    gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, outputTexture, 0);
    
    if (outputTexture === this.antTexture) {
      gl.viewport(0, 0, this.params.antCount, 1);
    } else {
      gl.viewport(0, 0, this.width, this.height);
    }
    
    gl.useProgram(program);
    this.setUniforms(program);
    
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');
    
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);
    gl.enableVertexAttribArray(texCoordLocation);
    gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);
    
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
  }

  private render() {
    const gl = this.gl;
    
    // Render to canvas
    gl.bindFramebuffer(gl.FRAMEBUFFER, null);
    gl.viewport(0, 0, this.canvas.width, this.canvas.height);
    
    gl.useProgram(this.renderProgram);
    this.setUniforms(this.renderProgram!);
    
    gl.bindBuffer(gl.ARRAY_BUFFER, this.quadBuffer);
    const positionLocation = gl.getAttribLocation(this.renderProgram!, 'a_position');
    const texCoordLocation = gl.getAttribLocation(this.renderProgram!, 'a_texCoord');
    
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 16, 0);
    gl.enableVertexAttribArray(texCoordLocation);
    gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, 16, 8);
    
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
  }

  private setUniforms(program: WebGLProgram) {
    const gl = this.gl;
    
    // Set texture uniforms
    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, this.antTexture);
    gl.uniform1i(gl.getUniformLocation(program, 'u_ants'), 0);
    
    gl.activeTexture(gl.TEXTURE1);
    gl.bindTexture(gl.TEXTURE_2D, this.pheromoneTexture);
    gl.uniform1i(gl.getUniformLocation(program, 'u_pheromones'), 1);
    
    gl.activeTexture(gl.TEXTURE2);
    gl.bindTexture(gl.TEXTURE_2D, this.obstacleTexture);
    gl.uniform1i(gl.getUniformLocation(program, 'u_obstacles'), 2);
    
    // Set parameter uniforms
    gl.uniform2f(gl.getUniformLocation(program, 'u_resolution'), this.width, this.height);
    gl.uniform1f(gl.getUniformLocation(program, 'u_sensorDistance'), this.params.sensorDistance);
    gl.uniform1f(gl.getUniformLocation(program, 'u_sensorAngle'), this.params.sensorAngle);
    gl.uniform1f(gl.getUniformLocation(program, 'u_turnSpeed'), this.params.turnSpeed);
    gl.uniform1f(gl.getUniformLocation(program, 'u_antSpeed'), this.params.antSpeed);
    gl.uniform1f(gl.getUniformLocation(program, 'u_evaporationRate'), this.params.evaporationRate);
    gl.uniform1f(gl.getUniformLocation(program, 'u_pheromoneStrength'), this.params.pheromoneStrength / 10);
    gl.uniform1i(gl.getUniformLocation(program, 'u_antCount'), this.params.antCount);
    gl.uniform1f(gl.getUniformLocation(program, 'u_time'), performance.now() / 1000);
    
    // Set nest and food positions
    if (this.nestPosition) {
      gl.uniform2f(gl.getUniformLocation(program, 'u_nestPos'), this.nestPosition.x, this.nestPosition.y);
    }
    
    const foodPositions = new Float32Array(8); // max 4 food sources
    for (let i = 0; i < Math.min(4, this.foodSources.length); i++) {
      foodPositions[i * 2] = this.foodSources[i].x;
      foodPositions[i * 2 + 1] = this.foodSources[i].y;
    }
    gl.uniform2fv(gl.getUniformLocation(program, 'u_foodPos'), foodPositions);
    gl.uniform1i(gl.getUniformLocation(program, 'u_foodCount'), Math.min(4, this.foodSources.length));
  }

  public setParams(params: SimulationParams) {
    this.params = params;
    if (params.antCount !== this.antData.length / 4) {
      this.antData = new Float32Array(params.antCount * 4);
      // Recreate ant texture with new size
      const gl = this.gl;
      if (this.antTexture) gl.deleteTexture(this.antTexture);
      this.antTexture = this.createTexture(params.antCount, 1, gl.RGBA32F);
      this.initializeAnts();
    }
  }

  public setObstacle(x: number, y: number, isObstacle: boolean) {
    const index = y * this.width + x;
    if (index >= 0 && index < this.obstacleData.length) {
      this.obstacleData[index] = isObstacle ? 255 : 0;
      
      // Upload to GPU
      const gl = this.gl;
      gl.bindTexture(gl.TEXTURE_2D, this.obstacleTexture);
      gl.texSubImage2D(gl.TEXTURE_2D, 0, 0, 0, this.width, this.height, gl.RED, gl.UNSIGNED_BYTE, this.obstacleData);
    }
  }

  public reset() {
    console.log('Resetting simulation');
    this.pheromoneData.fill(0);
    this.obstacleData.fill(0);
    
    // Upload cleared data to GPU
    const gl = this.gl;
    gl.bindTexture(gl.TEXTURE_2D, this.pheromoneTexture);
    gl.texSubImage2D(gl.TEXTURE_2D, 0, 0, 0, this.width, this.height, gl.RG, gl.FLOAT, this.pheromoneData);
    
    gl.bindTexture(gl.TEXTURE_2D, this.obstacleTexture);
    gl.texSubImage2D(gl.TEXTURE_2D, 0, 0, 0, this.width, this.height, gl.RED, gl.UNSIGNED_BYTE, this.obstacleData);
    
    // Reinitialize ants
    this.initializeAnts();
  }

  public dispose() {
    const gl = this.gl;
    
    // Clean up WebGL resources
    if (this.antTexture) gl.deleteTexture(this.antTexture);
    if (this.pheromoneTexture) gl.deleteTexture(this.pheromoneTexture);
    if (this.obstacleTexture) gl.deleteTexture(this.obstacleTexture);
    if (this.framebuffer) gl.deleteFramebuffer(this.framebuffer);
    if (this.antProgram) gl.deleteProgram(this.antProgram);
    if (this.pheromoneProgram) gl.deleteProgram(this.pheromoneProgram);
    if (this.renderProgram) gl.deleteProgram(this.renderProgram);
    if (this.quadBuffer) gl.deleteBuffer(this.quadBuffer);
    
    console.log('GPU simulation disposed');
  }
}
