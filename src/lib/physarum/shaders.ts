
// 通用顶点着色器
export const vertexShader = `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

// 代理（Agent）更新着色器
export const agentShaderFs = `
  varying vec2 vUv;
  uniform sampler2D uAgents;
  uniform sampler2D uTrails;
  uniform vec2 uResolution;
  uniform float uSensorAngle;
  uniform float uSensorOffset;
  uniform float uRotationAngle;
  uniform float uStepSize;
  uniform float uTime;
  uniform bool uMouseDown;
  uniform vec2 uMousePos;
  uniform float uAgentIndex;
  uniform float uAgentCount;

  float rand(vec2 co){
    return fract(sin(dot(co.xy ,vec2(12.9898,78.233))) * 43758.5453 + uTime);
  }

  void main() {
    vec4 agentData = texture2D(uAgents, vUv);
    vec2 pos = agentData.xy;
    float angle = agentData.z;
    float agentId = agentData.w;

    // 用户交互：如果鼠标按下，将一小部分代理移动到鼠标位置
    // 这也会“复活”处于死亡状态的代理
    if (uMouseDown) {
      float brush_radius = 5000.0;
      float dist = abs(agentId - uAgentIndex);
      // 处理环形ID的环绕距离
      dist = min(dist, uAgentCount - dist);

      if (dist < brush_radius) {
        // 添加一个小的随机偏移，以避免所有代理都精确地在同一个像素上
        vec2 randomOffset = vec2(rand(vUv) - 0.5, rand(vUv + vec2(1.0, 0.0)) - 0.5) * 0.1;
        gl_FragColor = vec4(uMousePos + randomOffset, rand(vUv) * 6.283, agentId);
        return;
      }
    }

    // 如果代理处于“死亡”状态（位置在屏幕外），则保持不变
    if (pos.x < 0.0) {
        gl_FragColor = agentData;
        return;
    }

    float sensorAngleRad = uSensorAngle * 3.14159 / 180.0;
    
    vec2 senseDirF = vec2(cos(angle), sin(angle));
    vec2 senseDirL = vec2(cos(angle + sensorAngleRad), sin(angle + sensorAngleRad));
    vec2 senseDirR = vec2(cos(angle - sensorAngleRad), sin(angle - sensorAngleRad));

    vec2 posF = pos + senseDirF * uSensorOffset / uResolution;
    vec2 posL = pos + senseDirL * uSensorOffset / uResolution;
    vec2 posR = pos + senseDirR * uSensorOffset / uResolution;

    float sampleF = texture2D(uTrails, posF).r;
    float sampleL = texture2D(uTrails, posL).r;
    float sampleR = texture2D(uTrails, posR).r;
    
    float rotationAngleRad = uRotationAngle * 3.14159 / 180.0;
    float newAngle = angle;

    if (sampleF > sampleL && sampleF > sampleR) {
      // no change
    } else if (sampleL > sampleR) {
      newAngle += rotationAngleRad;
    } else if (sampleR > sampleL) {
      newAngle -= rotationAngleRad;
    } else {
      newAngle += rotationAngleRad * (rand(vUv) > 0.5 ? 1.0 : -1.0);
    }
    
    vec2 newPos = pos + vec2(cos(newAngle), sin(newAngle)) * uStepSize / uResolution;

    if (newPos.x < 0.0) newPos.x += 1.0;
    if (newPos.x > 1.0) newPos.x -= 1.0;
    if (newPos.y < 0.0) newPos.y += 1.0;
    if (newPos.y > 1.0) newPos.y -= 1.0;

    gl_FragColor = vec4(newPos, newAngle, agentId);
  }
`;

// 轨迹图处理着色器（扩散与衰变）
export const processTrailShaderFs = `
  varying vec2 vUv;
  uniform sampler2D uTrails;
  uniform vec2 uResolution;
  uniform float uDecayFactor;
  
  void main() {
    vec2 texelSize = 1.0 / uResolution;
    vec4 sum = vec4(0.0);
    sum += texture2D(uTrails, vUv + vec2(-1.0, -1.0) * texelSize) * 0.05;
    sum += texture2D(uTrails, vUv + vec2( 0.0, -1.0) * texelSize) * 0.2;
    sum += texture2D(uTrails, vUv + vec2( 1.0, -1.0) * texelSize) * 0.05;
    sum += texture2D(uTrails, vUv + vec2(-1.0,  0.0) * texelSize) * 0.2;
    sum += texture2D(uTrails, vUv + vec2( 0.0,  0.0) * texelSize) * 1.0;
    sum += texture2D(uTrails, vUv + vec2( 1.0,  0.0) * texelSize) * 0.2;
    sum += texture2D(uTrails, vUv + vec2(-1.0,  1.0) * texelSize) * 0.05;
    sum += texture2D(uTrails, vUv + vec2( 0.0,  1.0) * texelSize) * 0.2;
    sum += texture2D(uTrails, vUv + vec2( 1.0,  1.0) * texelSize) * 0.05;
    
    vec4 blurred = sum / 2.1;
    gl_FragColor = blurred * uDecayFactor;
  }
`;

// 最终渲染着色器
export const renderShaderFs = `
  varying vec2 vUv;
  uniform sampler2D uTrails;
  
  void main() {
    float value = texture2D(uTrails, vUv).r;
    // 使用一个简单的颜色映射
    gl_FragColor = vec4(value * 0.8, value * 1.2, value * 1.5, 1.0);
  }
`;
