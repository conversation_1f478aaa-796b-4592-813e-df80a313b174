
// Ant Colony Optimization Simulation Core
export const ANT_STATE_SEARCHING = 'searching';
export const ANT_STATE_RETURNING = 'returning';

export type AntState = typeof ANT_STATE_SEARCHING | typeof ANT_STATE_RETURNING;

export interface Ant {
  id: number;
  x: number;
  y: number;
  angle: number;
  state: AntState;
  hasFood: boolean;
  lastPheromoneX: number;
  lastPheromoneY: number;
}

export interface Point {
  x: number;
  y: number;
}

export interface SimulationParams {
  antCount: number;
  evaporationRate: number;
  diffusionRate: number;
  sensorAngleSpacing: number;
  turnSpeed: number;
  sensorOffsetDst: number;
  sensorSize: number;
  moveSpeed: number;
}

export class AntColonySimulation {
  public ants: Ant[] = [];
  public width: number;
  public height: number;
  public nestPosition: Point;
  public foodSources: Point[] = [];
  public obstacles: Point[][] = [];
  public trails: { [key: string]: number } = {};
  
  private params: SimulationParams;

  constructor(width: number, height: number, params: SimulationParams) {
    this.width = width;
    this.height = height;
    this.params = params;
    this.nestPosition = { x: width / 2, y: height / 2 };
    this.initializeAnts();
  }

  private initializeAnts() {
    this.ants = [];
    for (let i = 0; i < this.params.antCount; i++) {
      const angle = Math.random() * Math.PI * 2;
      const radius = Math.random() * 20;
      this.ants.push({
        id: i,
        x: this.nestPosition.x + Math.cos(angle) * radius,
        y: this.nestPosition.y + Math.sin(angle) * radius,
        angle: Math.random() * Math.PI * 2,
        state: ANT_STATE_SEARCHING,
        hasFood: false,
        lastPheromoneX: this.nestPosition.x,
        lastPheromoneY: this.nestPosition.y
      });
    }
  }

  public setParams(params: SimulationParams) {
    const oldCount = this.params.antCount;
    this.params = params;
    
    if (params.antCount !== oldCount) {
      this.initializeAnts();
    }
  }

  public update() {
    this.ants.forEach(ant => this.updateAnt(ant));
    this.evaporateTrails();
  }

  private updateAnt(ant: Ant) {
    // Sensor positions
    const sensorAngle = this.params.sensorAngleSpacing;
    const sensorDist = this.params.sensorOffsetDst;
    
    const frontX = ant.x + Math.cos(ant.angle) * sensorDist;
    const frontY = ant.y + Math.sin(ant.angle) * sensorDist;
    const leftX = ant.x + Math.cos(ant.angle - sensorAngle) * sensorDist;
    const leftY = ant.y + Math.sin(ant.angle - sensorAngle) * sensorDist;
    const rightX = ant.x + Math.cos(ant.angle + sensorAngle) * sensorDist;
    const rightY = ant.y + Math.sin(ant.angle + sensorAngle) * sensorDist;

    const frontWeight = this.sampleTrail(frontX, frontY);
    const leftWeight = this.sampleTrail(leftX, leftY);
    const rightWeight = this.sampleTrail(rightX, rightY);

    const randomSteerStrength = Math.random();

    // Steering based on trail strength
    if (frontWeight > leftWeight && frontWeight > rightWeight) {
      // Continue straight
    } else if (frontWeight < leftWeight && frontWeight < rightWeight) {
      ant.angle += (randomSteerStrength - 0.5) * 2 * this.params.turnSpeed;
    } else if (rightWeight > leftWeight) {
      ant.angle += randomSteerStrength * this.params.turnSpeed;
    } else {
      ant.angle -= randomSteerStrength * this.params.turnSpeed;
    }

    // Random wandering
    ant.angle += (Math.random() - 0.5) * this.params.turnSpeed * 0.1;

    // Movement
    const newX = ant.x + Math.cos(ant.angle) * this.params.moveSpeed;
    const newY = ant.y + Math.sin(ant.angle) * this.params.moveSpeed;

    // Boundary checking
    if (newX >= 0 && newX < this.width && newY >= 0 && newY < this.height) {
      ant.x = newX;
      ant.y = newY;
    } else {
      ant.angle = Math.random() * Math.PI * 2;
    }

    // Check for food collection
    if (ant.state === ANT_STATE_SEARCHING) {
      for (const food of this.foodSources) {
        const dx = ant.x - food.x;
        const dy = ant.y - food.y;
        if (dx * dx + dy * dy < 100) { // 10px radius
          ant.state = ANT_STATE_RETURNING;
          ant.hasFood = true;
          break;
        }
      }
    }

    // Check for nest return
    if (ant.state === ANT_STATE_RETURNING) {
      const dx = ant.x - this.nestPosition.x;
      const dy = ant.y - this.nestPosition.y;
      if (dx * dx + dy * dy < 400) { // 20px radius
        ant.state = ANT_STATE_SEARCHING;
        ant.hasFood = false;
      }
    }

    // Deposit trail
    this.depositTrail(ant.x, ant.y);
  }

  private sampleTrail(x: number, y: number): number {
    const key = `${Math.floor(x / 5)},${Math.floor(y / 5)}`;
    return this.trails[key] || 0;
  }

  private depositTrail(x: number, y: number) {
    const key = `${Math.floor(x / 5)},${Math.floor(y / 5)}`;
    this.trails[key] = (this.trails[key] || 0) + 1;
  }

  private evaporateTrails() {
    for (const key in this.trails) {
      this.trails[key] *= this.params.evaporationRate;
      if (this.trails[key] < 0.1) {
        delete this.trails[key];
      }
    }
  }

  public addObstacle(points: Point[]) {
    this.obstacles.push(points);
  }

  public clearObstacles() {
    this.obstacles = [];
  }

  public reset() {
    this.initializeAnts();
    this.trails = {};
    this.obstacles = [];
  }
}
