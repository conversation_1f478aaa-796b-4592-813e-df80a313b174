export interface IsingParams {
  gridSize: number;
  temperature: number; // T
  externalField: number; // H
  thermalWeight: number; // 热运动影响权重
  enableThermalMotion: boolean; // 是否启用热运动
}

export type Spin = 1 | -1;
export type SpinGrid = Spin[][];

export const COUPLING_CONSTANT = 1.0; // J, 耦合常数

export const initialParams: IsingParams = {
  gridSize: 100,  // 增加网格大小以更好地观察相变现象
  temperature: 2.5,
  externalField: 0.0,
  thermalWeight: 1.0,
  enableThermalMotion: true,
};

export function createIsingGrid(size: number, initialState: 'Random' | 'Ordered'): SpinGrid {
  const grid: SpinGrid = [];
  for (let y = 0; y < size; y++) {
    grid[y] = [];
    for (let x = 0; x < size; x++) {
      const spin: Spin = initialState === 'Ordered' ? 1 : (Math.random() < 0.5 ? 1 : -1);
      grid[y][x] = spin;
    }
  }
  return grid;
}

export function calculateTotalEnergy(grid: SpinGrid, params: IsingParams): number {
  // Add safety checks
  if (!grid || grid.length === 0) {
    console.log('Grid is empty or undefined');
    return 0;
  }
  
  let energy = 0;
  const { gridSize, externalField } = params;
  const actualGridSize = grid.length;
  
  // Use the actual grid size instead of the parameter if they don't match
  const effectiveGridSize = Math.min(gridSize, actualGridSize);
  
  for (let y = 0; y < effectiveGridSize; y++) {
    // Check if the row exists
    if (!grid[y]) {
      console.log(`Grid row ${y} is undefined`);
      continue;
    }
    
    for (let x = 0; x < effectiveGridSize; x++) {
      // Check if the cell exists
      if (grid[y][x] === undefined) {
        console.log(`Grid cell [${y}][${x}] is undefined`);
        continue;
      }
      
      const spin = grid[y][x];
      
      // Calculate neighbors with safety checks
      const rightNeighbor = grid[y][(x + 1) % effectiveGridSize] || 0;
      const leftNeighbor = grid[y][(x - 1 + effectiveGridSize) % effectiveGridSize] || 0;
      const bottomNeighbor = grid[(y + 1) % effectiveGridSize] ? grid[(y + 1) % effectiveGridSize][x] || 0 : 0;
      const topNeighbor = grid[(y - 1 + effectiveGridSize) % effectiveGridSize] ? grid[(y - 1 + effectiveGridSize) % effectiveGridSize][x] || 0 : 0;
      
      const sumOfNeighbors = rightNeighbor + leftNeighbor + bottomNeighbor + topNeighbor;
      
      // 每个相互作用对只计算一次，所以除以2
      energy -= (COUPLING_CONSTANT * spin * sumOfNeighbors) / 2;
      energy -= externalField * spin;
    }
  }
  return energy;
}

export function calculateTotalMagnetization(grid: SpinGrid): number {
  // Add safety check
  if (!grid || grid.length === 0) {
    return 0;
  }
  
  let magnetization = 0;
  const size = grid.length;
  for (let y = 0; y < size; y++) {
    if (!grid[y]) continue;
    for (let x = 0; x < grid[y].length; x++) {
      if (grid[y][x] !== undefined) {
        magnetization += grid[y][x];
      }
    }
  }
  return magnetization;
}

// 执行一步蒙特卡洛更新
function singleMetropolisUpdate(
  grid: SpinGrid,
  temperature: number,
  externalField: number
): void {
  const gridSize = grid.length;
  
  // 1. 随机选择一个自旋
  const i = Math.floor(Math.random() * gridSize);
  const j = Math.floor(Math.random() * gridSize);
  const currentSpin = grid[i][j];

  // 2. 使用周期性边界条件计算近邻自旋和
  const top = grid[(i - 1 + gridSize) % gridSize][j];
  const bottom = grid[(i + 1) % gridSize][j];
  const left = grid[i][(j - 1 + gridSize) % gridSize];
  const right = grid[i][(j + 1) % gridSize];
  
  const neighborSum = top + bottom + left + right;

  // 3. 计算翻转能量变化：ΔE = 2s(Jm + H)，其中J=1
  const deltaE = 2 * currentSpin * (COUPLING_CONSTANT * neighborSum + externalField);

  // 4. 根据 Metropolis 准则决定是否翻转
  if (deltaE <= 0 || Math.random() < Math.exp(-deltaE / temperature)) {
    grid[i][j] *= -1; // 翻转自旋
  }
}

// 执行一个蒙特卡洛步（N*N次自旋翻转尝试）
export function metropolisStep(
  grid: SpinGrid,
  params: IsingParams,
  currentEnergy: number,
  currentMagnetization: number
): { newGrid: SpinGrid; newEnergy: number; newMagnetization: number } {
  // 安全检查
  if (!grid || grid.length === 0) {
    return { newGrid: grid, newEnergy: currentEnergy, newMagnetization: currentMagnetization };
  }
  
  const { temperature, externalField, thermalWeight, enableThermalMotion } = params;
  const newGrid = grid.map(row => [...row]); // 深拷贝网格
  const gridSize = newGrid.length;
  
  // 执行 N*N 次单步更新
  const updatesPerStep = gridSize * gridSize;
  for (let i = 0; i < updatesPerStep; i++) {
    if (!enableThermalMotion) {
      // 不考虑热运动时，使用原始温度
      singleMetropolisUpdate(newGrid, temperature, externalField);
    } else {
      // 考虑热运动时，使用调节后的温度
      const effectiveTemperature = temperature * thermalWeight;
      singleMetropolisUpdate(newGrid, effectiveTemperature, externalField);
    }
  }

  // 更新能量和磁化强度
  const newEnergy = calculateTotalEnergy(newGrid, params);
  const newMagnetization = calculateTotalMagnetization(newGrid);

  return { newGrid, newEnergy, newMagnetization };
}

// 新增：计算磁化率所需的统计量
export interface MagnetizationStats {
  M: number;      // 瞬时磁化强度
  M2: number;     // 瞬时磁化强度的平方
  absM: number;   // 磁化强度绝对值
}

export function calculateMagnetizationStats(grid: SpinGrid): MagnetizationStats {
  const M = calculateTotalMagnetization(grid);
  return {
    M,
    M2: M * M,
    absM: Math.abs(M)
  };
}

// 检查系统是否达到热平衡
function hasReachedEquilibrium(
  history: number[],
  windowSize: number = 50  // 减小窗口大小以更快响应
): boolean {
  if (history.length < windowSize * 2) return false;
  
  const recentWindow = history.slice(-windowSize);
  const previousWindow = history.slice(-2 * windowSize, -windowSize);
  
  const recentMean = recentWindow.reduce((a, b) => a + b) / windowSize;
  const recentVar = recentWindow.reduce((a, b) => a + (b - recentMean) ** 2, 0) / windowSize;
  
  const previousMean = previousWindow.reduce((a, b) => a + b) / windowSize;
  const previousVar = previousWindow.reduce((a, b) => a + (b - previousMean) ** 2, 0) / windowSize;
  
  // 检查均值和方差的相对变化
  const meanChange = Math.abs(recentMean - previousMean) / (Math.abs(previousMean) + 1e-6);
  const varChange = Math.abs(recentVar - previousVar) / (Math.abs(previousVar) + 1e-6);
  
  return meanChange < 0.1 && varChange < 0.1;
}

// 计算磁化率
export function calculateSusceptibility(
  magnetizationHistory: number[],
  temperature: number,
  gridSize: number,
  equilibrationSteps: number = 200
): number {
  // 确保有足够的数据点
  if (magnetizationHistory.length < equilibrationSteps) {
    return 0;
  }

  const N = gridSize * gridSize;
  const T = temperature;
  
  // 使用最近的平衡态数据
  const equilibratedData = magnetizationHistory.slice(-500);
  
  // 计算平均磁化强度和磁化强度平方的平均值
  const avgM = equilibratedData.reduce((sum, m) => sum + m, 0) / equilibratedData.length;
  const avgM2 = equilibratedData.reduce((sum, m) => sum + m * m, 0) / equilibratedData.length;
  
  // 计算磁化率：χ = (⟨M²⟩ - ⟨M⟩²)/(NT)
  const susceptibility = (avgM2 - avgM * avgM) / (N * T);
  
  return susceptibility;
}
