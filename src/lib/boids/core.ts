
// 一个简单的2D向量类
export class Vector {
  x: number;
  y: number;

  constructor(x = 0, y = 0) {
    this.x = x;
    this.y = y;
  }

  add(v: Vector) {
    this.x += v.x;
    this.y += v.y;
    return this;
  }

  sub(v: Vector) {
    this.x -= v.x;
    this.y -= v.y;
    return this;
  }

  mult(n: number) {
    this.x *= n;
    this.y *= n;
    return this;
  }

  div(n: number) {
    this.x /= n;
    this.y /= n;
    return this;
  }

  magSq() {
    return this.x * this.x + this.y * this.y;
  }

  mag() {
    return Math.sqrt(this.magSq());
  }

  normalize() {
    const len = this.mag();
    if (len !== 0) this.mult(1 / len);
    return this;
  }

  setMag(n: number) {
    return this.normalize().mult(n);
  }

  limit(max: number) {
    const mSq = this.magSq();
    if (mSq > max * max) {
      this.div(Math.sqrt(mSq)).mult(max);
    }
    return this;
  }
  
  static sub(v1: Vector, v2: Vector): Vector {
      return new Vector(v1.x - v2.x, v1.y - v2.y);
  }

  static dist(v1: Vector, v2: Vector): number {
    return Math.sqrt(Math.pow(v1.x - v2.x, 2) + Math.pow(v1.y - v2.y, 2));
  }
}

export interface BoidsParams {
  width: number;
  height: number;
  boidCount: number;
  separationWeight: number;
  alignmentWeight: number;
  cohesionWeight: number;
  perceptionRadius: number;
  separationRadius: number;
  maxSpeed: number;
  maxForce: number;
}

export const initialBoidsParams: Omit<BoidsParams, 'width' | 'height'> = {
  boidCount: 150,
  separationWeight: 1.5,
  alignmentWeight: 1.0,
  cohesionWeight: 1.0,
  perceptionRadius: 50,
  separationRadius: 25,
  maxSpeed: 4.0,
  maxForce: 0.1,
};

export class Boid {
  position: Vector;
  velocity: Vector;
  acceleration: Vector;
  params: BoidsParams;

  constructor(x: number, y: number, params: BoidsParams) {
    this.position = new Vector(x, y);
    this.velocity = new Vector(Math.random() * 2 - 1, Math.random() * 2 - 1);
    this.velocity.setMag(Math.random() * 2 + 2);
    this.acceleration = new Vector();
    this.params = params;
  }

  private applyForce(force: Vector) {
    this.acceleration.add(force);
  }
  
  private separate(boids: Boid[]): Vector {
    const steer = new Vector();
    let total = 0;
    for (const other of boids) {
      const d = Vector.dist(this.position, other.position);
      if (other !== this && d > 0 && d < this.params.separationRadius) {
        const diff = Vector.sub(this.position, other.position);
        diff.div(d * d); // Weight by distance
        steer.add(diff);
        total++;
      }
    }
    if (total > 0) {
      steer.div(total);
      steer.setMag(this.params.maxSpeed);
      steer.sub(this.velocity);
      steer.limit(this.params.maxForce);
    }
    return steer;
  }

  private align(boids: Boid[]): Vector {
    const steer = new Vector();
    let total = 0;
    for (const other of boids) {
      const d = Vector.dist(this.position, other.position);
      if (other !== this && d < this.params.perceptionRadius) {
        steer.add(other.velocity);
        total++;
      }
    }
    if (total > 0) {
      steer.div(total);
      steer.setMag(this.params.maxSpeed);
      steer.sub(this.velocity);
      steer.limit(this.params.maxForce);
    }
    return steer;
  }

  private cohere(boids: Boid[]): Vector {
    const cohesion = new Vector();
    let total = 0;
    for (const other of boids) {
      const d = Vector.dist(this.position, other.position);
      if (other !== this && d < this.params.perceptionRadius) {
        cohesion.add(other.position);
        total++;
      }
    }
    if (total > 0) {
      cohesion.div(total);
      const desired = Vector.sub(cohesion, this.position);
      desired.setMag(this.params.maxSpeed);
      const steer = Vector.sub(desired, this.velocity);
      steer.limit(this.params.maxForce);
      return steer;
    }
    return new Vector();
  }

  flock(boids: Boid[]) {
    this.acceleration.mult(0);
    const separation = this.separate(boids);
    const alignment = this.align(boids);
    const cohesion = this.cohere(boids);

    separation.mult(this.params.separationWeight);
    alignment.mult(this.params.alignmentWeight);
    cohesion.mult(this.params.cohesionWeight);
    
    this.applyForce(separation);
    this.applyForce(alignment);
    this.applyForce(cohesion);
  }

  update() {
    this.velocity.add(this.acceleration);
    this.velocity.limit(this.params.maxSpeed);
    this.position.add(this.velocity);
  }
  
  edges() {
    if (this.position.x > this.params.width) {
      this.position.x = 0;
    } else if (this.position.x < 0) {
      this.position.x = this.params.width;
    }
    if (this.position.y > this.params.height) {
      this.position.y = 0;
    } else if (this.position.y < 0) {
      this.position.y = this.params.height;
    }
  }
}
