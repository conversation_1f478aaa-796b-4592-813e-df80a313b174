
export type AgentType = 0 | 1 | 2; // 0: Empty, 1: Type A, 2: Type B
export type Grid = AgentType[][];
export type NeighborhoodType = 'Moore' | '<PERSON> Neumann';
export type BoundaryCondition = 'Periodic' | 'Cut-off';

export interface SchellingParams {
  gridSize: number;
  similarityThreshold: number; // as a fraction, e.g., 0.35 for 35%
  ratioTypeA: number; // as a fraction
  emptyCellsPct: number; // as a fraction
  neighborhoodType: NeighborhoodType;
  boundaryCondition: BoundaryCondition;
}

export interface SchellingStats {
  round: number;
  happyAgentPct: number;
  segregationIndex: number;
  isConverged: boolean;
}

// Helper to shuffle an array
export function shuffle<T>(array: T[]): T[] {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}
