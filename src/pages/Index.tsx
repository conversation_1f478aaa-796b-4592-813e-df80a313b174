
import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import GameOfLifePage from "@/components/game-of-life/GameOfLifePage";
import SchellingSegregationPage from "@/components/schelling-segregation/SchellingSegregationPage";
import IsingModelPage from "@/components/ising-model/IsingModelPage";
import PhysarumPage from "@/components/physarum-simulation/PhysarumPage";
import BoidsPage from "@/components/boids-simulation/BoidsPage";

const Index = () => {
  const [activeTab, setActiveTab] = useState("game-of-life");

  return (
    <div className="min-h-screen w-full bg-background text-foreground p-4 md:p-8 dark">
      <header className="text-center mb-8">
        <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight gradient-title">
          EMERGENCE ｜ 复杂 · 涌现 ｜ SHOWROOM
        </h1>
      </header>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 md:grid-cols-5">
          <TabsTrigger 
            value="game-of-life" 
            className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400 data-[state=active]:after:bg-cyan-400"
          >
            生命游戏
          </TabsTrigger>
          <TabsTrigger 
            value="system2"
            className="data-[state=active]:bg-amber-500/20 data-[state=active]:text-amber-400 data-[state=active]:after:bg-amber-400"
          >
            谢林隔离模型
          </TabsTrigger>
          <TabsTrigger 
            value="system3"
            className="data-[state=active]:bg-violet-500/20 data-[state=active]:text-violet-400 data-[state=active]:after:bg-violet-400"
          >
            二维伊辛模型
          </TabsTrigger>
          <TabsTrigger 
            value="system4"
            className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400 data-[state=active]:after:bg-green-400"
          >
            蚁群觅食行为
          </TabsTrigger>
          <TabsTrigger 
            value="system5"
            className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-sky-400 data-[state=active]:after:bg-sky-400"
          >
            鸟群行为模型
          </TabsTrigger>
        </TabsList>
        <TabsContent value="game-of-life" className="mt-4">
          <GameOfLifePage isActive={activeTab === "game-of-life"} />
        </TabsContent>
        <TabsContent value="system2" className="mt-4">
          <SchellingSegregationPage isActive={activeTab === "system2"} />
        </TabsContent>
        <TabsContent value="system3" className="mt-4">
          <IsingModelPage isActive={activeTab === "system3"} />
        </TabsContent>
        <TabsContent value="system4" className="mt-4">
          <PhysarumPage isActive={activeTab === "system4"} />
        </TabsContent>
        <TabsContent value="system5" className="mt-4">
          <BoidsPage isActive={activeTab === "system5"} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Index;
