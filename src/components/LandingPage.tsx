
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';

const LandingPage = () => {
  const navigate = useNavigate();

  const handleExplore = () => {
    navigate('/explore');
  };

  return (
    <div className="min-h-screen w-full bg-background text-foreground flex flex-col items-center justify-center p-4 dark">
      <div className="text-center space-y-8 max-w-4xl mx-auto">
        {/* 两行标题格式 - 单行显示 */}
        <div className="space-y-10">
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-extrabold tracking-tight gradient-title-chinese whitespace-nowrap">
            ｜复杂 · 涌现｜
          </h1>
          {/* 分割线 - 白色半透明，极细，长度不超过中文标题 */}
          <div className="flex justify-center">
            <div className="w-48 md:w-64 lg:w-80 h-px bg-white opacity-20"></div>
          </div>
          <h2 className="text-3xl md:text-3xl lg:text-6xl font-bold tracking-wider gradient-title-english whitespace-nowrap">
            EMERGENCE SHOWROOM
          </h2>
        </div>
        
        {/* 副标题 - 解释涌现现象 */}
        <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
          在复杂系统中，简单的个体通过相互作用产生出意想不到的集体行为。
          从细胞自动机的生命游戏到群体智能的涌现，探索那些令人惊叹的自组织现象。
        </p>
        
        {/* 探索按钮 */}
        <div className="pt-4">
          <Button
            onClick={handleExplore}
            className="breathing-button text-white font-semibold px-8 py-4 text-lg rounded-lg hover:scale-105 transition-all duration-300 shadow-lg"
          >
            开始探索
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
