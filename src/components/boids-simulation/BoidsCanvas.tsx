
import React, { useRef, useEffect, useState } from 'react';
import { Boid, BoidsParams } from '@/lib/boids/core';

interface BoidsCanvasProps {
  flock: Boid[];
  params: BoidsParams;
  isRunning: boolean;
  onCanvasClick: (x: number, y: number) => void;
}

const BoidsCanvas: React.FC<BoidsCanvasProps> = ({ flock, params, isRunning, onCanvasClick }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameId = useRef<number>();
  const [isMouseDown, setIsMouseDown] = useState(false);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);
  const intervalRef = useRef<NodeJS.Timeout>();

  // 生成正态分布随机数的函数
  const generateNormalRandom = (mean: number, stdDev: number) => {
    let u = 0, v = 0;
    while(u === 0) u = Math.random(); // Converting [0,1) to (0,1)
    while(v === 0) v = Math.random();
    const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    return z * stdDev + mean;
  };

  // 创建随机方向和速度的 Boid
  const createRandomBoid = (x: number, y: number) => {
    const angle = Math.random() * 2 * Math.PI;
    const speed = Math.abs(generateNormalRandom(params.maxSpeed, params.maxSpeed * 0.3));
    const vx = Math.cos(angle) * speed;
    const vy = Math.sin(angle) * speed;
    
    const boid = new Boid(x, y, params);
    boid.velocity.x = vx;
    boid.velocity.y = vy;
    return boid;
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    flock.forEach(boid => boid.params = params);

    const render = () => {
      ctx.fillStyle = 'rgb(15, 23, 42)'; // Dark Slate Blue
      ctx.fillRect(0, 0, params.width, params.height);

      for (const boid of flock) {
        if (isRunning) {
            boid.flock(flock);
            boid.update();
            boid.edges();
        }
        
        const boidSize = 7;
        ctx.save();
        ctx.translate(boid.position.x, boid.position.y);
        ctx.rotate(Math.atan2(boid.velocity.y, boid.velocity.x));
        
        ctx.beginPath();
        ctx.moveTo(boidSize, 0);
        ctx.lineTo(-boidSize / 2, -boidSize / 2);
        ctx.lineTo(-boidSize / 2, boidSize / 2);
        ctx.closePath();
        
        ctx.fillStyle = '#06b6d4'; // Cyan color
        ctx.fill();
        
        ctx.restore();
      }

      animationFrameId.current = requestAnimationFrame(render);
    };

    render();

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [flock, params, isRunning]);

  // 处理连续发射粒子
  useEffect(() => {
    if (isMouseDown && mousePosition) {
      intervalRef.current = setInterval(() => {
        onCanvasClick(mousePosition.x, mousePosition.y);
      }, 100); // 每100ms发射一个粒子
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isMouseDown, mousePosition, onCanvasClick]);

  const getMousePosition = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return null;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    const x = (event.clientX - rect.left) * scaleX;
    const y = (event.clientY - rect.top) * scaleY;
    return { x, y };
  };

  const handleMouseDown = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const position = getMousePosition(event);
    if (position) {
      setIsMouseDown(true);
      setMousePosition(position);
      // 立即发射一个粒子
      onCanvasClick(position.x, position.y);
    }
  };

  const handleMouseUp = () => {
    setIsMouseDown(false);
    setMousePosition(null);
  };

  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (isMouseDown) {
      const position = getMousePosition(event);
      if (position) {
        setMousePosition(position);
      }
    }
  };

  const handleMouseLeave = () => {
    setIsMouseDown(false);
    setMousePosition(null);
  };

  return (
    <canvas
      ref={canvasRef}
      width={params.width}
      height={params.height}
      className="w-full h-full cursor-pointer"
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    />
  );
};

export default BoidsCanvas;
