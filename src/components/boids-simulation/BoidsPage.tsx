
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Boid, BoidsParams, initialBoidsParams, Vector } from '@/lib/boids/core';
import BoidsCanvas from './BoidsCanvas';
import BoidsControlPanel from './BoidsControlPanel';
import BoidsRulesExplanation from './BoidsRulesExplanation';

interface BoidsPageProps {
  isActive: boolean;
}

const BoidsPage: React.FC<BoidsPageProps> = ({ isActive }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [params, setParams] = useState<BoidsParams>({
      ...initialBoidsParams,
      width: 800,
      height: 600
  });
  const [flock, setFlock] = useState<Boid[]>([]);
  const [isRunning, setIsRunning] = useState(true);

  // 生成正态分布随机数的函数
  const generateNormalRandom = (mean: number, stdDev: number) => {
    let u = 0, v = 0;
    while(u === 0) u = Math.random();
    while(v === 0) v = Math.random();
    const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    return z * stdDev + mean;
  };

  const createFlock = useCallback((p: BoidsParams) => {
    const newFlock: Boid[] = [];
    for (let i = 0; i < p.boidCount; i++) {
        newFlock.push(new Boid(Math.random() * p.width, Math.random() * p.height, p));
    }
    setFlock(newFlock);
  }, []);

  const reset = useCallback(() => {
    if(containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        const newParams = { ...initialBoidsParams, width: clientWidth, height: clientHeight };
        setParams(newParams);
        createFlock(newParams);
        setIsRunning(true);
    }
  }, [createFlock]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current && isActive) {
        const { clientWidth, clientHeight } = containerRef.current;
        setParams(p => ({ ...p, width: clientWidth, height: clientHeight }));
      }
    };

    if (isActive) {
      reset(); // Initial setup
    }
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [reset, isActive]);

  const togglePlay = useCallback(() => setIsRunning(prev => !prev), []);
  
  const addBoid = useCallback((x: number, y: number) => {
    // 创建随机方向和速度的 Boid
    const angle = Math.random() * 2 * Math.PI;
    const speed = Math.abs(generateNormalRandom(params.maxSpeed, params.maxSpeed * 0.3));
    const vx = Math.cos(angle) * speed;
    const vy = Math.sin(angle) * speed;
    
    const boid = new Boid(x, y, params);
    boid.velocity = new Vector(vx, vy);
    
    setFlock(currentFlock => [...currentFlock, boid]);
  }, [params, generateNormalRandom]);

  const handleSetParams = useCallback((newControlParams: Partial<Omit<BoidsParams, 'width' | 'height'>>) => {
    setParams(currentParams => {
      const updatedParams = { ...currentParams, ...newControlParams };
      if (
        newControlParams.boidCount !== undefined &&
        newControlParams.boidCount !== currentParams.boidCount
      ) {
        createFlock(updatedParams);
      }
      return updatedParams;
    });
  }, [createFlock]);

  const { width, height, ...controlParams } = params;
  
  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-150px)] w-full bg-background">
      <div ref={containerRef} className="flex-grow h-full w-full md:w-auto relative border-2 border-cyan-300/30 rounded-lg overflow-hidden">
        <BoidsCanvas
          flock={flock}
          params={params}
          isRunning={isRunning && isActive}
          onCanvasClick={addBoid}
        />
        <BoidsRulesExplanation />
      </div>
      <BoidsControlPanel
        isRunning={isRunning}
        togglePlay={togglePlay}
        reset={reset}
        params={controlParams}
        setParams={handleSetParams}
        boidCount={flock.length}
      />
    </div>
  );
};

export default BoidsPage;
