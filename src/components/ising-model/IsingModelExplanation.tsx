import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const IsingModelExplanation = () => {
  return (
    <div className="absolute bottom-4 left-4 max-w-md bg-black/60 rounded-xl text-xs backdrop-blur border-2 border-violet-500/20">
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="px-4 py-2 text-base font-semibold text-violet-300 hover:text-violet-400 hover:no-underline [&[data-state=open]>svg]:rotate-180">
            <div className="flex items-center gap-2">
              <span className="text-xl">⚛️</span>
              伊辛模型：磁性的量子舞蹈
            </div>
          </AccordionTrigger>
          <AccordionContent className="text-sm text-violet-100/80 space-y-3 px-4 pb-3">
            <p className="leading-relaxed">
              伊辛模型是物理学中研究<span className="text-violet-300">相变现象</span>的经典模型，它展示了温度如何影响材料的<span className="text-violet-300">磁性行为</span>。
            </p>
            
            <h4 className="font-bold text-violet-300">物理机制：</h4>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-xl">🔄</span>
                <span><strong className="text-violet-400">自旋状态：</strong> 每个格点代表一个磁矩，可以向上或向下</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-xl">🌡️</span>
                <span><strong className="text-violet-400">温度效应：</strong> 高温导致无序，低温促进有序排列</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-xl">🔋</span>
                <span><strong className="text-violet-400">能量关系：</strong> 相邻自旋同向时能量较低，更稳定</span>
              </li>
            </ul>

            <div className="space-y-2">
              <h4 className="font-bold text-violet-300">临界现象：</h4>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <span className="text-xl">💫</span>
                  <span>在临界温度(≈2.269)附近观察剧烈的相变</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-xl">📊</span>
                  <span>关注磁化强度和能量随温度的变化</span>
                </li>
              </ul>
            </div>

            <p className="text-violet-300/90 italic">
              通过调节温度，观察系统如何在有序与混沌之间优雅转换，体验统计物理的魅力！
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default IsingModelExplanation;
