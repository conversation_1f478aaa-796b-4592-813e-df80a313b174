import React, { useRef, useState } from 'react';

interface Props {
  magnetization: number;
  susceptibility: number;
  stepCount: number;
  isThermalized: boolean;
  temperature: number;
}

const PANEL_SIZE = 160;

const IsingModelMagnetizationPanel: React.FC<Props> = ({ magnetization, susceptibility, stepCount, isThermalized, temperature }) => {
  const panelRef = useRef<HTMLDivElement>(null);
  const [dragging, setDragging] = useState(false);
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const handleMouseDown = (e: React.MouseEvent) => {
    setDragging(true);
    const rect = panelRef.current?.getBoundingClientRect();
    setOffset({
      x: e.clientX - (rect?.left ?? 0),
      y: e.clientY - (rect?.top ?? 0),
    });
    e.stopPropagation();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!dragging) return;
    setPosition({
      x: e.clientX - offset.x,
      y: e.clientY - offset.y,
    });
  };

  const handleMouseUp = () => setDragging(false);

  React.useEffect(() => {
    if (dragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [dragging, offset]);

  return (
    <div
      ref={panelRef}
      style={{
        width: PANEL_SIZE,
        height: PANEL_SIZE,
        position: 'absolute',
        right: position.x || 16,
        bottom: position.y || 16,
        zIndex: 20,
        cursor: dragging ? 'grabbing' : 'grab',
        userSelect: 'none',
        transition: dragging ? 'none' : 'box-shadow 0.2s',
      }}
      className="bg-violet-950/80 border-2 border-violet-400/40 rounded-xl shadow-lg flex flex-col items-center justify-center text-xs text-violet-100 select-none"
      onMouseDown={handleMouseDown}
    >
      <div className="font-bold text-violet-300 text-base mb-1 flex items-center gap-1">
        🧲 磁性
      </div>
      <div className="flex-1 flex flex-col items-center justify-center gap-1 w-full">
        <div className="flex flex-col items-start w-full px-2 gap-1">
          <div><span className="text-violet-400 font-semibold">步数</span> <span className="ml-1">{stepCount} {!isThermalized && <span className='text-yellow-400'>(热化中)</span>}</span></div>
          <div><span className="text-violet-400 font-semibold">标准化磁化强度</span> <span className="ml-1">{magnetization.toFixed(4)}</span></div>
          <div><span className="text-violet-400 font-semibold">磁化率</span> <span className="ml-1">{susceptibility.toFixed(6)}</span></div>
          <div><span className="text-violet-400 font-semibold">温度</span> <span className="ml-1">{temperature.toFixed(3)}{temperature >= 2.2 && temperature <= 2.3 && <span className='text-pink-400'> (临界区)</span>}</span></div>
        </div>
      </div>
    </div>
  );
};

export default IsingModelMagnetizationPanel;
