
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

export interface PlotDataPoint {
  T: number; // Temperature
  chi: number; // Susceptibility
  step?: number; // Optional step number
}

interface IsingModelPlotsProps {
  data: PlotDataPoint[];
}

const IsingModelPlots: React.FC<IsingModelPlotsProps> = ({ data }) => {
  console.log('Plot data:', data); // 调试信息
  
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>磁化率 (χ) vs 温度 (T)</CardTitle>
          <CardDescription>
            等待数据收集中... 需要经过热化阶段后开始采样。
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 w-full flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <div>数据收集中，请稍候...</div>
              <div className="text-xs mt-2 text-violet-400">
                临界温度 Tc ≈ 2.269<br/>
                建议在2.0-2.5温度范围观察相变
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 按温度排序数据点
  const sortedData = [...data].sort((a, b) => a.T - b.T);
  
  // 寻找磁化率峰值
  const maxChi = Math.max(...sortedData.map(d => d.chi));
  const peakPoint = sortedData.find(d => d.chi === maxChi);

  return (
    <Card>
      <CardHeader>
        <CardTitle>磁化率 (χ) vs 温度 (T)</CardTitle>
        <CardDescription>
          磁化率在临界温度 Tc ≈ 2.269 附近发散。当前检测到的峰值温度：{peakPoint ? peakPoint.T.toFixed(3) : 'N/A'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart 
              data={sortedData} 
              margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis 
                type="number" 
                dataKey="T"
                domain={['dataMin - 0.05', 'dataMax + 0.05']}
                tick={{ fontSize: 12 }} 
                label={{ 
                  value: "温度 (T)", 
                  position: 'insideBottom', 
                  offset: -10, 
                  fontSize: 14,
                  fontWeight: 'bold' 
                }} 
              />
              <YAxis 
                type="number"
                dataKey="chi"
                tick={{ fontSize: 12 }} 
                domain={['auto', 'auto']} 
                label={{ 
                  value: "磁化率 (χ)", 
                  angle: -90, 
                  position: 'insideLeft', 
                  fontSize: 14,
                  fontWeight: 'bold'
                }}
              />
              <Tooltip 
                cursor={{ strokeDasharray: '3 3' }}
                contentStyle={{
                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                  border: '1px solid #ccc',
                  borderRadius: '6px',
                  fontSize: '12px'
                }}
                formatter={(value: number) => [value.toFixed(6), '磁化率 (χ)']}
                labelFormatter={(value) => `温度: ${Number(value).toFixed(3)}`}
              />
              <Line 
                type="monotone"
                dataKey="chi" 
                stroke="#ff6b6b" 
                strokeWidth={2}
                dot={{ fill: '#ff4757', strokeWidth: 1, r: 3 }}
                activeDot={{ r: 5, fill: '#ff4757' }}
              />
              {/* 标记理论临界温度 */}
              <Line 
                type="monotone"
                dataKey={() => maxChi * 0.1}
                stroke="#888888"
                strokeDasharray="5 5"
                strokeWidth={1}
                dot={false}
                activeDot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-4 text-sm text-muted-foreground space-y-1">
          <div>数据点数量: {sortedData.length}</div>
          {sortedData.length > 0 && (
            <>
              <div>温度范围: {sortedData[0].T.toFixed(3)} - {sortedData[sortedData.length - 1].T.toFixed(3)}</div>
              <div>最大磁化率: {maxChi.toFixed(6)} (T = {peakPoint ? peakPoint.T.toFixed(3) : 'N/A'})</div>
              <div className="text-violet-400">理论临界温度: Tc ≈ 2.269</div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default IsingModelPlots;
