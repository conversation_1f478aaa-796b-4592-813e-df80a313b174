import React, { useRef, useEffect, useState } from 'react';
import { Grid } from '@/lib/schelling-segregation/core';

interface SchellingCanvasProps {
  grid: Grid;
  agentAColor: string;
  agentBColor: string;
  emptyCellColor: string;
  latticeCount: number; // 从晶格数量滑杆获取的值
}

const SchellingCanvas: React.FC<SchellingCanvasProps> = ({
  grid,
  agentAColor,
  agentBColor,
  emptyCellColor,
  latticeCount,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

  // 监听容器尺寸变化
  useEffect(() => {
    const updateSize = () => {
      const container = containerRef.current;
      if (!container) return;
      
      const rect = container.getBoundingClientRect();
      setContainerSize({
        width: rect.width,
        height: rect.height
      });
    };

    // 创建 ResizeObserver 监听容器尺寸变化
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // 初始更新
    updateSize();

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // 渲染晶格矩阵
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || grid.length === 0 || containerSize.width === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置画布的物理像素尺寸
    const dpr = window.devicePixelRatio || 1;
    canvas.width = containerSize.width * dpr;
    canvas.height = containerSize.height * dpr;

    // 设置画布的 CSS 尺寸
    canvas.style.width = `${containerSize.width}px`;
    canvas.style.height = `${containerSize.height}px`;

    // 应用设备像素比缩放
    ctx.scale(dpr, dpr);

    // 使用容器宽度计算晶格大小，确保填满画布
    const cellSize = Math.ceil(containerSize.width / latticeCount);

    console.log('Container size:', containerSize.width, 'x', containerSize.height);
    console.log('Lattice count:', latticeCount);
    console.log('Cell size:', cellSize);

    // 清空画布
    ctx.clearRect(0, 0, containerSize.width, containerSize.height);

    // 不需要偏移，直接从(0,0)开始绘制，确保填满整个画布
    const offsetX = 0;
    const offsetY = 0;

    // 绘制晶格矩阵
    for (let y = 0; y < grid.length; y++) {
      for (let x = 0; x < grid[0].length; x++) {
        const agentType = grid[y][x];
        
        // 设置颜色
        if (agentType === 1) {
          ctx.fillStyle = agentAColor;
        } else if (agentType === 2) {
          ctx.fillStyle = agentBColor;
        } else {
          ctx.fillStyle = emptyCellColor;
        }

        // 计算晶格位置和绘制
        const xPos = Math.floor(offsetX + x * cellSize);
        const yPos = Math.floor(offsetY + y * cellSize);
        
        ctx.fillRect(xPos, yPos, cellSize, cellSize);
        
        // 添加网格线
        ctx.strokeStyle = '#1a1a1a';
        ctx.lineWidth = 0.5;
        ctx.strokeRect(xPos, yPos, cellSize, cellSize);
      }
    }
  }, [grid, agentAColor, agentBColor, emptyCellColor, latticeCount, containerSize]);

  return (
    <div ref={containerRef} className="w-full h-full relative">
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0"
        style={{ 
          imageRendering: 'pixelated',
        }}
      />
    </div>
  );
};

export default SchellingCanvas;
