import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

const SchellingRulesExplanation: React.FC = () => {
  return (
    <div className="absolute bottom-4 left-4 max-w-md bg-black/60 rounded-xl text-xs backdrop-blur border-2 border-amber-500/20">
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="px-4 py-2 text-base font-semibold text-amber-300 hover:text-amber-400 hover:no-underline [&[data-state=open]>svg]:rotate-180">
            <div className="flex items-center gap-2">
              <span className="text-xl">🏘️</span>
              谢林模型：微观选择与宏观隔离
            </div>
          </AccordionTrigger>
          <AccordionContent className="text-sm text-amber-100/80 space-y-3 px-4 pb-3">
            <p className="leading-relaxed">
              谢林隔离模型展示了一个引人深思的现象：即使人们只有<span className="text-amber-300">轻微的偏好</span>，也可能导致意想不到的<span className="text-amber-300">社会隔离</span>。
            </p>
            
            <h4 className="font-bold text-amber-300">行为规则：</h4>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-xl">🏠</span>
                <span><strong className="text-amber-400">个体偏好：</strong> 每个个体都希望周围有一定比例的相似邻居</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-xl">🚶</span>
                <span><strong className="text-amber-400">迁移决策：</strong> 当不满意时，个体会搬到随机的空位置</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-xl">🌟</span>
                <span><strong className="text-amber-400">邻里关系：</strong> 可以选择考虑四邻域或八邻域的影响</span>
              </li>
            </ul>

            <div className="space-y-2">
              <h4 className="font-bold text-amber-300">观察重点：</h4>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <span className="text-xl">🎯</span>
                  <span>相似性阈值对隔离程度的影响</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-xl">📊</span>
                  <span>群体比例与空位率的平衡关系</span>
                </li>
              </ul>
            </div>

            <p className="text-amber-300/90 italic">
              小小的个体偏好如何导致大规模的社会分化？这个简单而深刻的模型或许能带来一些启发。
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default SchellingRulesExplanation;
