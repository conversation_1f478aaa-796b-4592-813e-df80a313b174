import React, { useState, useRef, useEffect } from 'react';
import { useGameOfLife } from '@/hooks/useGameOfLife';
import GameCanvas from './GameCanvas';
import ControlPanel from './ControlPanel';
import RulesExplanation from './RulesExplanation';
import { patterns } from '@/lib/game-of-life/patterns';
import { GameRules } from '@/lib/game-of-life/core';

const initialRules: GameRules = {
  survivalMin: 2,
  survivalMax: 3,
  birthMin: 3,
  birthMax: 3,
};

interface GameOfLifePageProps {
  isActive: boolean;
}

const GameOfLifePage: React.FC<GameOfLifePageProps> = ({ isActive }) => {
  const [config, setConfig] = useState({
    width: 100,
    height: 100,
    speed: 50,
    wrapEdges: true,
  });
  const canvasContainerRef = useRef<HTMLDivElement>(null);

  const [rules, setRules] = useState<GameRules>(initialRules);

  const [colors, setColors] = useState({
    liveCellColor: '#00FFFF', // Cyan
    deadCellColor: '#111827', // Dark gray
    gridLineColor: '#374151', // Lighter gray
  });

  const [openAccordion, setOpenAccordion] = useState<string | undefined>();

  useEffect(() => {
    const container = canvasContainerRef.current;
    if (!container) return;

    const CELL_SIZE = 10; // Adjust this to change cell density

    const observer = new ResizeObserver(() => {
      const { width, height } = container.getBoundingClientRect();
      if (width > 0 && height > 0) {
        const newGridWidth = Math.floor(width / CELL_SIZE);
        const newGridHeight = Math.floor(height / CELL_SIZE);
        
        setConfig(c => {
          if (c.width !== newGridWidth || c.height !== newGridHeight) {
            return { ...c, width: newGridWidth, height: newGridHeight };
          }
          return c;
        });
      }
    });

    observer.observe(container);
    
    // Set initial size
    const { width, height } = container.getBoundingClientRect();
    if (width > 0 && height > 0) {
      const newGridWidth = Math.floor(width / CELL_SIZE);
      const newGridHeight = Math.floor(height / CELL_SIZE);
      setConfig(c => ({ ...c, width: newGridWidth, height: newGridHeight }));
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  const game = useGameOfLife({ ...config, rules, isActive });

  const handleLoadPattern = (patternName: string) => {
    const pattern = patterns[patternName];
    if (pattern) {
      game.loadPattern(pattern);
    }
  };

  const handleReset = () => {
    game.reset();
    setRules(initialRules);
  };

  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-150px)] w-full bg-background">
      <div ref={canvasContainerRef} className="flex-grow h-full w-full md:w-auto relative border-2 border-cyan-400/50 rounded-lg overflow-hidden">
        <GameCanvas 
          grid={game.grid}
          width={config.width}
          height={config.height}
          setCellState={game.setCellState}
          liveCellColor={colors.liveCellColor}
          deadCellColor={colors.deadCellColor}
          gridLineColor={colors.gridLineColor}
        />
        <RulesExplanation />
      </div>
      <ControlPanel
        isRunning={game.isRunning}
        togglePlay={game.togglePlay}
        step={game.step}
        reset={handleReset}
        simulationSpeed={config.speed}
        setSimulationSpeed={(speed) => setConfig(c => ({...c, speed}))}
        wrapEdges={config.wrapEdges}
        setWrapEdges={(wrap) => setConfig(c => ({...c, wrapEdges: wrap}))}
        loadPattern={handleLoadPattern}
        liveCellColor={colors.liveCellColor}
        setLiveCellColor={(color) => setColors(c => ({...c, liveCellColor: color}))}
        deadCellColor={colors.deadCellColor}
        setDeadCellColor={(color) => setColors(c => ({...c, deadCellColor: color}))}
        generation={game.generation}
        liveCellCount={game.liveCellCount}
        rules={rules}
        setRules={setRules}
        openAccordion={openAccordion}
        setOpenAccordion={setOpenAccordion}
      />
    </div>
  );
};

export default GameOfLifePage;
