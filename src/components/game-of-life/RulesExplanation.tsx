import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

const RulesExplanation: React.FC = () => {
  return (
    <div className="absolute bottom-4 left-4 max-w-md bg-black/60 rounded-xl text-xs backdrop-blur border-2 border-cyan-500/20">
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="px-4 py-2 text-base font-semibold text-cyan-300 hover:text-cyan-400 hover:no-underline [&[data-state=open]>svg]:rotate-180">
            <div className="flex items-center gap-2">
              <span className="text-xl">🦠</span>
              康威生命游戏：生命的律动
            </div>
          </AccordionTrigger>
          <AccordionContent className="text-sm text-cyan-100/80 space-y-3 px-4 pb-3">
            <p className="leading-relaxed">
              康威生命游戏是一个展示如何通过简单规则创造复杂生命图案的<span className="text-cyan-300">元胞自动机</span>。每个细胞的生死都取决于它周围邻居的状态。
            </p>
            
            <h4 className="font-bold text-cyan-300">基本规则：</h4>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-xl">🌱</span>
                <span><strong className="text-cyan-400">诞生：</strong> 死细胞周围恰好有3个活细胞时重获新生</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-xl">🌿</span>
                <span><strong className="text-cyan-400">存活：</strong> 活细胞需要2-3个活邻居才能继续存活</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-xl">💀</span>
                <span><strong className="text-cyan-400">死亡：</strong> 过度孤独（少于2）或过度拥挤（大于3）的细胞将死亡</span>
              </li>
            </ul>

            <div className="space-y-2">
              <h4 className="font-bold text-cyan-300">互动提示：</h4>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <span className="text-xl">👆</span>
                  <span>点击网格即可切换细胞的生死状态</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-xl">🎯</span>
                  <span>尝试创建滑翔机、脉冲星等经典图案</span>
                </li>
              </ul>
            </div>

            <p className="text-cyan-300/90 italic">
              探索这个微观世界，观察简单规则如何孕育出丰富多彩的生命形态！
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default RulesExplanation;
