
import React, { useRef, useEffect } from 'react';

interface GPUCanvasProps {
  className?: string;
  onCanvasReady?: (canvas: HTMLCanvasElement) => void;
}

const GPUCanvas: React.FC<GPUCanvasProps> = ({ className, onCanvasReady }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (canvasRef.current && onCanvasReady) {
      onCanvasReady(canvasRef.current);
    }
  }, [onCanvasReady]);

  return (
    <canvas 
      ref={canvasRef} 
      className={className}
      style={{ imageRendering: 'pixelated' }}
    />
  );
};

export default GPUCanvas;
