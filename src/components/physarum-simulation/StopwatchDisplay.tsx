import React from 'react';

interface StopwatchDisplayProps {
    elapsedTime: number;
    isCompleted: boolean;
    completionTime: number | null;
}

const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = (seconds % 60).toFixed(1);
    return `${minutes}:${remainingSeconds.padStart(4, '0')}`;
};

const StopwatchDisplay: React.FC<StopwatchDisplayProps> = ({
    elapsedTime,
    isCompleted,
    completionTime
}) => {
    const currentTimeDisplay = formatTime(elapsedTime);
    const completionTimeDisplay = completionTime ? formatTime(completionTime) : null;

    return (
        <div className="space-y-2">
            <div className="flex items-center justify-between">
                <span className="text-green-200">累计用时</span>
                <span className="font-mono text-green-400">{currentTimeDisplay}</span>
            </div>
            <div className="flex items-center justify-between">
                <span className="text-green-200">95%完成用时</span>
                <span className={`font-mono ${isCompleted ? 'text-green-400' : 'text-green-400/50'}`}>
                    {completionTimeDisplay || '--:--.-'}
                </span>
            </div>
        </div>
    );
};

export default StopwatchDisplay;
