import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";
import { Button } from "../ui/button";
import { Label } from "../ui/label";
import { Slider } from "../ui/slider";
import { Settings2, Play, Pause, RotateCcw, Trash2 } from "lucide-react";
import { SimulationConfig, SimulationStats } from '../../lib/physarum/ant-colony';
import SimulationStatsPanel from './SimulationStatsPanel';

interface AntColonyControlPanelProps {
  config: SimulationConfig;
  setConfig: (config: SimulationConfig) => void;
  isRunning: boolean;
  onPause: () => void;
  onReset: () => void;
  onClearObstacles: () => void;
  stats: SimulationStats | null;
}

const AntColonyControlPanel: React.FC<AntColonyControlPanelProps> = ({
  config,
  setConfig,
  isRunning,
  onPause,
  onReset,
  onClearObstacles,
  stats
}) => {
  return (
    <div className="h-full flex flex-col overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-green-600/20 hover:scrollbar-thumb-green-600/40">
      {/* 页面标题和简介 */}
      <div className="flex-none p-6 pb-4">
        <h2 className="text-xl font-semibold text-green-400 mb-2">蚁群觅食模拟</h2>
        <p className="text-sm text-green-200/80 leading-relaxed">
          通过模拟蚁群在寻找食物过程中的集体智能行为，展示了生物群体如何通过简单的个体行为和信息素通信，创造出高效的觅食路径网络。你可以通过点击添加食物源，按住Shift绘制障碍物，观察蚂蚁们如何适应环境变化。
        </p>
      </div>
      
      <div className="flex-1 space-y-4 overflow-y-auto px-6 pb-6 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-green-600/20 hover:scrollbar-thumb-green-600/40">
        {/* 控制按钮 */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            onClick={() => onPause()}
            className={
              isRunning
                ? "border-green-500/50 text-green-400 hover:bg-green-500/10"
                : "bg-green-500 text-black hover:bg-green-400 hover:text-black"
            }
            variant={isRunning ? "outline" : "default"}
          >
            {isRunning ? (
              <Pause className="mr-2 h-5 w-5" />
            ) : (
              <Play className="mr-2 h-5 w-5" />
            )}
            {isRunning ? "暂停模拟" : "开始模拟"}
          </Button>
          <Button
            onClick={onReset}
            variant="outline"
            className="border-green-500/50 text-green-400 hover:bg-green-500/10"
          >
            <RotateCcw className="mr-2 h-5 w-5" />
            重置
          </Button>
        </div>
        <Button
          onClick={onClearObstacles}
          variant="outline"
          className="w-full border-green-500/50 text-green-400 hover:bg-green-500/10"
        >
          <Trash2 className="mr-2 h-5 w-5" />
          清除障碍物
        </Button>

        {/* 参数调节区域 */}
        <div className="space-y-6 bg-green-950/20 rounded-lg p-4 border border-green-500/20">
          {/* 蚂蚁数量 */}
          <div className="space-y-3">
            <div className="flex justify-between">
              <Label className="text-green-200">蚂蚁数量</Label>
              <span className="text-green-400">{config.antCount}</span>
            </div>
            <Slider
              value={[config.antCount]}
              min={10}
              max={500}
              step={10}
              className="[&>span:first-child]:bg-green-950 [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500"
              onValueChange={([value]) =>
                setConfig({ ...config, antCount: value })
              }
            />
          </div>

          {/* 信息素强度 */}
          <div className="space-y-3">
            <div className="flex justify-between">
              <Label className="text-green-200">信息素强度</Label>
              <span className="text-green-400">{config.pheromoneDepositRate}</span>
            </div>
            <Slider
              value={[config.pheromoneDepositRate]}
              min={1}
              max={5}
              step={0.5}
              className="[&>span:first-child]:bg-green-950 [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500"
              onValueChange={([value]) =>
                setConfig({ ...config, pheromoneDepositRate: value })
              }
            />
          </div>

          {/* 蒸发率 */}
          <div className="space-y-3">
            <div className="flex justify-between">
              <Label className="text-green-200">蒸发率</Label>
              <span className="text-green-400">
                {(config.evaporationRate * 100).toFixed(1)}%
              </span>
            </div>
            <Slider
              value={[config.evaporationRate]}
              min={0.95}
              max={0.999}
              step={0.001}
              className="[&>span:first-child]:bg-green-950 [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500"
              onValueChange={([value]) =>
                setConfig({ ...config, evaporationRate: value })
              }
            />
          </div>
        </div>

        {/* 高级参数 */}
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="advanced-settings" className="border-green-500/20">
            <AccordionTrigger className="text-green-300 hover:text-green-400">
              <div className="flex items-center gap-2">
                <Settings2 className="h-4 w-4" />
                高级参数调节
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-4">
              {/* 移动速度 */}
              <div className="space-y-3">
                <div className="flex justify-between">
                  <Label className="text-green-200">移动速度</Label>
                  <span className="text-green-400">{config.speed}</span>
                </div>
                <Slider
                  value={[config.speed]}
                  min={0.5}
                  max={3}
                  step={0.1}
                  className="[&>span:first-child]:bg-green-950 [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500"
                  onValueChange={([value]) =>
                    setConfig({ ...config, speed: value })
                  }
                />
              </div>

              {/* 传感器距离 */}
              <div className="space-y-3">
                <div className="flex justify-between">
                  <Label className="text-green-200">传感器距离</Label>
                  <span className="text-green-400">{config.sensorDistance}</span>
                </div>
                <Slider
                  value={[config.sensorDistance]}
                  min={10}
                  max={30}
                  step={1}
                  className="[&>span:first-child]:bg-green-950 [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500"
                  onValueChange={([value]) =>
                    setConfig({ ...config, sensorDistance: value })
                  }
                />
              </div>

              {/* 转向强度 */}
              <div className="space-y-3">
                <div className="flex justify-between">
                  <Label className="text-green-200">转向强度</Label>
                  <span className="text-green-400">{config.steerStrength}</span>
                </div>
                <Slider
                  value={[config.steerStrength]}
                  min={0.05}
                  max={0.2}
                  step={0.01}
                  className="[&>span:first-child]:bg-green-950 [&>span>span]:bg-green-500 [&>span:last-child]:border-green-500"
                  onValueChange={([value]) =>
                    setConfig({ ...config, steerStrength: value })
                  }
                />
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* 图例说明 */}
        <div className="mt-4 p-4 rounded-lg border border-green-500/20 bg-green-950/20 space-y-2">
          <h3 className="font-medium text-green-400 mb-3">图例说明</h3>
          <ul className="space-y-2 text-sm">
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-white" />
              <span className="text-green-200">搜寻蚂蚁</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-yellow-400" />
              <span className="text-green-200">携带食物的蚂蚁</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-lime-500" />
              <span className="text-green-200">食物信息素</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-green-200">返巢信息素</span>
            </li>
          </ul>
        </div>

        {/* 状态统计面板 */}
        {stats && <SimulationStatsPanel stats={stats} />}
      </div>
    </div>
  );
};

export default AntColonyControlPanel;
