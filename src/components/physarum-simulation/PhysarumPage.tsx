import React, { useState, useEffect, useRef, useCallback } from 'react';
import { AntColonyOptimization } from '@/lib/physarum/ant-colony';
import type { SimulationConfig, Vector, SimulationStats } from '@/lib/physarum/ant-colony';
import AntColonyControlPanel from './PhysarumControlPanel';
import AntColonyRulesExplanation from './PhysarumRulesExplanation';
import AntColonyCanvas from './AntColonyCanvas';

const initialConfig: SimulationConfig = {
  antCount: 200,
  speed: 1.8,
  steerStrength: 0.1,
  wanderStrength: 0.05,
  homingStrength: 0.01,
  pheromoneDepositRate: 2,
  evaporationRate: 0.98,
  sensorAngleDegrees: 35,
  sensorDistance: 20,
  foodSize: 5,
  obstacleDetectionRadius: 15,
  obstacleRepulsion: 0.4,
};

interface AntColonyPageProps {
  isActive: boolean;
}

const AntColonyPage: React.FC<AntColonyPageProps> = ({ isActive }) => {
  const [config, setConfig] = useState<SimulationConfig>(initialConfig);
  const [isRunning, setIsRunning] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);
  const [stats, setStats] = useState<SimulationStats | null>(null);
  
  const simulationRef = useRef<AntColonyOptimization | null>(null);
  const currentWallRef = useRef<Vector[]>([]);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const initializeSimulation = useCallback(() => {
    if (!isActive || simulationRef.current) return;
    
    const simulation = new AntColonyOptimization(800, 600, config);
    simulationRef.current = simulation;
    
    // 添加初始食物源
    const baseDistance = 200;
    const centerX = 400;
    const centerY = 300;
    
    for (let i = 0; i < 4; i++) {
      const angle = (i * Math.PI / 2) + (Math.random() * 0.5 - 0.25);
      const distance = baseDistance + Math.random() * 50;
      simulation.addFood(centerX + Math.cos(angle) * distance, centerY + Math.sin(angle) * distance);
    }
  }, [isActive]);

  const resetSimulation = useCallback(() => {
    simulationRef.current = null;
    initializeSimulation();
    setIsRunning(false);
  }, [initializeSimulation]);

  // 增量更新配置
  const handleConfigUpdate = useCallback((newConfig: SimulationConfig) => {
    setConfig(newConfig);
    
    if (simulationRef.current) {
      simulationRef.current.config = newConfig;
      updateStats();
    }
  }, []);

  // 状态更新函数
  const updateStats = useCallback(() => {
    if (simulationRef.current) {
      setStats(simulationRef.current.getStatistics());
    }
  }, []);

  // 使用 requestAnimationFrame 持续更新状态
  useEffect(() => {
    let animationFrameId: number;
    
    const updateLoop = () => {
      updateStats();
      if (isRunning) {
        animationFrameId = requestAnimationFrame(updateLoop);
      }
    };

    if (isRunning) {
      animationFrameId = requestAnimationFrame(updateLoop);
    }

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isRunning, updateStats]);

  const handleAddFood = useCallback((x: number, y: number) => {
    if (simulationRef.current && !isDrawing) {
      simulationRef.current.addFood(x, y);
      updateStats();
    }
  }, [isDrawing, updateStats]);

  const handleStartDrawing = useCallback((x: number, y: number) => {
    setIsDrawing(true);
    currentWallRef.current = [{ x, y }];
    if (simulationRef.current) {
      simulationRef.current.addObstacle(currentWallRef.current);
    }
  }, []);

  const handleContinueDrawing = useCallback((x: number, y: number) => {
    if (isDrawing) {
      currentWallRef.current.push({ x, y });
    }
  }, [isDrawing]);

  const handleStopDrawing = useCallback(() => {
    setIsDrawing(false);
    currentWallRef.current = [];
  }, []);

  const clearObstacles = useCallback(() => {
    if (simulationRef.current) {
      simulationRef.current.clearObstacles();
    }
  }, []);

  const toggleSimulation = useCallback(() => {
    if (!isRunning && !simulationRef.current) {
      initializeSimulation();
    }
    setIsRunning(!isRunning);
  }, [isRunning, initializeSimulation]);

  useEffect(() => {
    if (isActive) {
      initializeSimulation();
    }
    return () => {
      simulationRef.current = null;
    };
  }, [initializeSimulation, isActive]);

  return (
    <div className="flex gap-2 h-[calc(100vh-150px)]">
      <div className="flex-1 relative overflow-hidden rounded-xl border-2 border-green-500/20">
        <AntColonyCanvas
          simulation={isRunning ? simulationRef.current : null}
          onAddFood={handleAddFood}
          onStartDrawing={handleStartDrawing}
          onContinueDrawing={handleContinueDrawing}
          onStopDrawing={handleStopDrawing}
          isDrawing={isDrawing}
        />
        <AntColonyRulesExplanation />
      </div>
      <div className="w-[380px] bg-background/50 backdrop-blur-sm overflow-hidden">
        <AntColonyControlPanel
          config={config}
          setConfig={handleConfigUpdate}
          isRunning={isRunning}
          onPause={toggleSimulation}
          onReset={resetSimulation}
          onClearObstacles={clearObstacles}
          stats={stats}
        />
      </div>
    </div>
  );
};

export default AntColonyPage;
