import React from 'react';
import StopwatchDisplay from './StopwatchDisplay';

interface SimulationStats {
  foodStats: {
    collected: number;
    total: number;
    percentage: string;
    elapsedTime: number;
    isCompleted: boolean;
    completionTime: number | null;
  };
  antStats: {
    searching: number;
    returning: number;
    total: number;
  };
}

interface SimulationStatsPanelProps {
    stats: SimulationStats;
}

const SimulationStatsPanel: React.FC<SimulationStatsPanelProps> = ({ stats }) => {
    return (
        <div className="p-4 rounded-lg border border-green-500/20 bg-green-950/20 space-y-4">
            <div className="space-y-2">
                <h3 className="text-sm font-medium text-green-400">食物收集状态</h3>
                <div className="space-y-1">
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-green-200">已收集</span>
                        <span className="text-green-400">{stats.foodStats.collected} / {stats.foodStats.total} ({stats.foodStats.percentage}%)</span>
                    </div>
                    {/* 秒表显示 */}
                    <div className="mt-2 text-sm">
                        <StopwatchDisplay 
                            elapsedTime={stats.foodStats.elapsedTime}
                            isCompleted={stats.foodStats.isCompleted}
                            completionTime={stats.foodStats.completionTime}
                        />
                    </div>
                </div>
                <div className="w-full h-2 bg-green-950 rounded-full overflow-hidden">
                    <div 
                        className="h-full bg-green-500 transition-all duration-300"
                        style={{ width: `${stats.foodStats.percentage}%` }}
                    />
                </div>
            </div>

            <div className="space-y-2">
                <h3 className="text-sm font-medium text-green-400">蚂蚁状态分布</h3>
                <div className="grid gap-2">
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-green-200">觅食蚂蚁</span>
                        <span className="text-green-400">{stats.antStats.searching}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                        <span className="text-green-200">返巢蚂蚁</span>
                        <span className="text-green-400">{stats.antStats.returning}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm border-t border-green-500/20 pt-2">
                        <span className="text-green-200">总数</span>
                        <span className="text-green-400">{stats.antStats.total}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SimulationStatsPanel;
