import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

const AntColonyRulesExplanation: React.FC = () => {
  return (
    <div className="absolute bottom-4 left-4 max-w-md bg-black/60 rounded-xl text-xs backdrop-blur border-2 border-green-500/20">
      <Accordion type="single" collapsible>
        <AccordionItem value="item-1" className="border-none">
          <AccordionTrigger className="px-4 py-2 text-base font-semibold text-green-300 hover:text-green-400 hover:no-underline [&[data-state=open]>svg]:rotate-180">
            <div className="flex items-center gap-2">
              <span className="text-xl">🐜</span>
              蚁群觅食：路径的智慧
            </div>
          </AccordionTrigger>
          <AccordionContent className="text-sm text-green-100/80 space-y-3 px-4 pb-3">
            <p className="leading-relaxed">
              本模拟展示了群体智能中的"施蒂格默"现象：个体通过改变环境来进行间接通信。大量简单的"蚂蚁"遵循几条基本规则，却能涌现出寻找最优路径的复杂集体行为。
            </p>
            <h4 className="font-bold text-green-300">核心规则：</h4>
            <ul className="space-y-2">
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 mt-1.5 rounded-full bg-white shrink-0"></span>
                <span><strong className="text-green-400">寻找食物：</strong> 蚂蚁留下"觅食信息素"(绿色)，并被"返程信息素"(蓝色)吸引，以找到回家的路。</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 mt-1.5 rounded-full bg-yellow-400 shrink-0"></span>
                <span><strong className="text-green-400">返回巢穴：</strong> 找到食物后，蚂蚁留下"返程信息素"，并被"觅食信息素"吸引，以沿着已建立的路径返回。</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="w-2 h-2 mt-1.5 rounded-full bg-green-400 shrink-0"></span>
                <span><strong className="text-green-400">信息素扩散：</strong> 所有信息素都会随时间缓慢蒸发和扩散。这使得更短、更常用的路径信息素浓度更高，从而被强化；而长路径则被"遗忘"。</span>
              </li>
            </ul>
            <p className="text-green-300/90 italic">
              观察蚁群如何在您设置的障碍物周围，自适应地建立起最高效的觅食通道。
            </p>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default AntColonyRulesExplanation;
