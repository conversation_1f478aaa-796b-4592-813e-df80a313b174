// 简单的磁化率计算测试
// 模拟修复后的磁化率计算逻辑

function calculateSusceptibility(magnetizationHistory, temperature, gridSize) {
  const equilibrationSteps = 100;
  
  if (magnetizationHistory.length < equilibrationSteps + 50) {
    return 0;
  }

  const N = gridSize * gridSize;
  const T = temperature;
  const BOLTZMANN_CONSTANT = 1.0;

  const sampleSize = Math.min(300, magnetizationHistory.length - equilibrationSteps);
  const recentData = magnetizationHistory.slice(-sampleSize);

  // 计算归一化的磁矩 - 保持原始符号！
  const normalizedData = recentData.map(m => m / N);

  // 计算统计量
  const averageM = normalizedData.reduce((sum, m) => sum + m, 0) / normalizedData.length;
  const averageM2 = normalizedData.reduce((sum, m) => sum + m * m, 0) / normalizedData.length;

  // 计算磁化率：χ = N * (⟨M²⟩ - ⟨M⟩²) / (kT)
  const magnetizationVariance = averageM2 - averageM * averageM;
  const susceptibility = N * magnetizationVariance / (BOLTZMANN_CONSTANT * T);

  return Math.max(0, susceptibility);
}

// 测试案例1：模拟高温情况（随机磁化强度，小涨落）
console.log("=== 测试案例1：高温情况 (T=3.0) ===");
const highTempData = [];
for (let i = 0; i < 500; i++) {
  // 高温时磁化强度在0附近小幅波动
  highTempData.push((Math.random() - 0.5) * 200); // 小的随机涨落
}
const highTempSusceptibility = calculateSusceptibility(highTempData, 3.0, 100);
console.log(`高温磁化率: ${highTempSusceptibility.toFixed(6)}`);

// 测试案例2：模拟临界温度情况（大涨落）
console.log("\n=== 测试案例2：临界温度情况 (T=2.269) ===");
const criticalTempData = [];
for (let i = 0; i < 500; i++) {
  // 临界温度时磁化强度有大的涨落
  criticalTempData.push((Math.random() - 0.5) * 8000); // 大的随机涨落
}
const criticalTempSusceptibility = calculateSusceptibility(criticalTempData, 2.269, 100);
console.log(`临界温度磁化率: ${criticalTempSusceptibility.toFixed(6)}`);

// 测试案例3：模拟低温情况（磁化强度接近饱和，小涨落）
console.log("\n=== 测试案例3：低温情况 (T=1.5) ===");
const lowTempData = [];
for (let i = 0; i < 500; i++) {
  // 低温时磁化强度接近饱和值，小涨落
  lowTempData.push(9000 + (Math.random() - 0.5) * 500); // 接近饱和，小涨落
}
const lowTempSusceptibility = calculateSusceptibility(lowTempData, 1.5, 100);
console.log(`低温磁化率: ${lowTempSusceptibility.toFixed(6)}`);

// 测试案例4：对比使用绝对值的错误计算
console.log("\n=== 测试案例4：错误的绝对值计算对比 ===");
function calculateSusceptibilityWrong(magnetizationHistory, temperature, gridSize) {
  const equilibrationSteps = 100;
  
  if (magnetizationHistory.length < equilibrationSteps + 50) {
    return 0;
  }

  const N = gridSize * gridSize;
  const T = temperature;
  const BOLTZMANN_CONSTANT = 1.0;

  const sampleSize = Math.min(300, magnetizationHistory.length - equilibrationSteps);
  const recentData = magnetizationHistory.slice(-sampleSize);

  // 错误：使用绝对值！
  const normalizedData = recentData.map(m => Math.abs(m) / N);

  const averageM = normalizedData.reduce((sum, m) => sum + m, 0) / normalizedData.length;
  const averageM2 = normalizedData.reduce((sum, m) => sum + m * m, 0) / normalizedData.length;

  const magnetizationVariance = averageM2 - averageM * averageM;
  const susceptibility = N * magnetizationVariance / (BOLTZMANN_CONSTANT * T);

  return Math.max(0, susceptibility);
}

const wrongSusceptibility = calculateSusceptibilityWrong(criticalTempData, 2.269, 100);
console.log(`错误计算（使用绝对值）: ${wrongSusceptibility.toFixed(6)}`);
console.log(`正确计算（保持符号）: ${criticalTempSusceptibility.toFixed(6)}`);

console.log("\n=== 结论 ===");
console.log("1. 修复后的计算应该在临界温度附近显示最大的磁化率");
console.log("2. 使用绝对值的错误计算会导致磁化率接近0");
console.log("3. 正确的计算保持了磁化强度的符号，能够正确反映涨落");
