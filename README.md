# 复杂系统的涌现特性展示

## 简介

这是一个集成多种“涌现现象”模拟的交互式可视化平台。你可以在不同的复杂系统中亲手体会由简单规则涌现出的宏观行为，几个例子涵盖了生命游戏、社会模型、物理模型、群体智能等多个经典案例。用户可在线体验、交互和观察这些模型的动态演化过程，通过互动操作直观感受“复杂性科学”的魅力。

## 在线体验
本项目已经部署在 lovable 服务器，欢迎体验。
https://cellular-cosmos-explorer.lovable.app/

## 喜欢本项目，请不要吝啬右上角的 star！
<p align="center">
    <a href="https://github.com/freemank1224/cellular-cosmos-explorer" target="_blank">
        <img src="https://img.shields.io/github/stars/freemank1224/cellular-cosmos-explorer?style=social" alt="GitHub stars" />
    </a>
</p>
---

## 复杂系统项目

### 1. 康威生命游戏（Game of life）

- **背景**：由数学家 John Conway 提出，是最著名的元胞自动机之一。通过简单的生死规则，展现出丰富多样的生命图案和自组织现象。
- **功能与玩法**：用户可自定义初始图案，观察细胞群体的繁衍、消亡与稳定结构。支持手动绘制、随机生成、暂停/继续等操作。

### 2. 谢林隔离模型（Schelling segregation model）

- **背景**：由经济学家 Thomas Schelling 提出，用于解释微观偏好如何导致宏观社会隔离现象。
- **功能与玩法**：模拟两类个体在城市中的迁移与聚集。用户可调整“容忍度”等参数，观察社会结构如何从混合走向分离。

### 3. 二维伊辛模型（2D Ising model）

- **背景**：物理学中研究磁性材料相变的经典模型。展示温度变化下自旋系统的有序-无序转变。
- **功能与玩法**：用户可调节温度，观察磁化强度、临界现象和自旋团簇的形成与消失。

### 4. 蚁群觅食模拟（Ant colony foraging）

- **背景**：模拟蚂蚁通过信息素间接通信，集体寻找最优路径的过程，体现“施蒂格默”与群体智能。
- **功能与玩法**：用户可添加食物、设置障碍，观察蚂蚁如何自组织出高效觅食路径。支持信息素热力图可视化。

### 5. 鸟群行为模型（Boids flocking）

- **背景**：Craig Reynolds 提出的 Boids 算法，模拟鸟群、鱼群等生物的集体运动。
- **功能与玩法**：用户可调整分离、对齐、聚集等参数，体验局部规则如何产生流畅的群体舞动。

---

## 本地部署方法

1. **克隆仓库**
   ```bash
   git clone https://github.com/freemank1224/cellular-cosmos-explorer.git
   cd cellular-cosmos-explorer
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```
   打开浏览器访问 http://localhost:5173 （或终端提示的端口）即可体验全部模拟。

---

## 下一步工作计划

- [ ] 整合AI功能
- [ ] 增加更多复杂系统与人工生命模型（如：流行病传播、生态系统等）
- [ ] 丰富参数调节与实验记录功能
- [ ] 增加导出动画/图片等分享功能

---

## 致谢

本项目基于 [lovable](https://lovable.dev/) 平台构建，lovable 提供了高效的前端开发与无缝部署体验！

---

如有建议或反馈，欢迎 issue 或 pr！
